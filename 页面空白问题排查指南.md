# 页面空白问题排查指南

## 问题描述
APP用户管理页面点击"个人简介"按钮和个人简介管理页面点击"详情"按钮后，页面显示空白。

## 已实施的修复措施

### 1. 路由配置修复
- 修正了个人简介详情页面的路由配置
- 从 `/fuguang/profile/detail/index` 改为 `/fuguang/profile/detail`
- 添加了测试页面路由 `/fuguang/profile/test`

### 2. 组件错误处理
- 添加了数据初始化，避免undefined错误
- 添加了API调用的错误处理和加载状态
- 添加了调试信息和错误提示

### 3. 依赖导入修复
- 添加了parseTime工具函数的导入
- 确保所有必要的API函数正确导入

## 排查步骤

### 第一步：测试路由是否正常
1. 启动前端项目
2. 在APP用户管理页面点击"个人简介"按钮
3. 应该跳转到测试页面 `/fuguang/profile/test`
4. 检查页面是否正常显示，查看控制台是否有错误

### 第二步：测试API接口
1. 在测试页面点击"测试API接口"按钮
2. 查看是否能正常调用后端接口
3. 检查网络请求是否成功，返回数据是否正确

### 第三步：检查后端服务
如果API调用失败，需要检查：
1. 后端服务是否正常启动
2. 个人简介相关的Controller是否正确配置
3. 数据库表是否创建成功
4. 权限配置是否正确

### 第四步：检查前端控制台
打开浏览器开发者工具，查看：
1. Console标签页是否有JavaScript错误
2. Network标签页是否有请求失败
3. 路由跳转是否正确

## 常见问题及解决方案

### 1. 路由404错误
**现象**: 页面跳转后显示404
**解决**: 检查路由配置是否正确，组件文件是否存在

### 2. API接口404错误
**现象**: 网络请求返回404
**解决**: 
- 检查后端Controller是否正确配置
- 确认RequestMapping路径是否正确
- 检查后端服务是否启动

### 3. 权限错误
**现象**: 页面显示"无权限访问"
**解决**:
- 检查菜单权限配置
- 确认用户角色是否有相应权限
- 执行权限配置SQL脚本

### 4. 数据库错误
**现象**: API调用返回数据库相关错误
**解决**:
- 检查数据库表是否创建
- 确认Mapper XML配置是否正确
- 检查数据库连接是否正常

## 调试命令

### 检查后端日志
```bash
# 查看后端启动日志
tail -f logs/sys-info.log

# 查看错误日志
tail -f logs/sys-error.log
```

### 检查数据库
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'app_user_profile';
SHOW TABLES LIKE 'app_user_timeline';

-- 检查菜单权限
SELECT * FROM sys_menu WHERE menu_name LIKE '%个人简介%';
```

### 前端调试
```javascript
// 在浏览器控制台执行
console.log('当前路由:', this.$route);
console.log('路由参数:', this.$route.query);
```

## 恢复步骤

如果测试完成，需要恢复正常跳转：

1. 修改 `fuguang-web/src/views/fuguang/appuser/index.vue` 中的 `handleViewProfile` 方法
2. 将路径从 `/fuguang/profile/test` 改回 `/fuguang/profile/detail`

```javascript
handleViewProfile(row) {
  this.$router.push({
    path: '/fuguang/profile/detail',
    query: { userId: row.userId, userName: row.userName }
  });
}
```

## 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. 网络请求的详细信息
3. 后端日志中的相关错误
4. 数据库表的创建状态
