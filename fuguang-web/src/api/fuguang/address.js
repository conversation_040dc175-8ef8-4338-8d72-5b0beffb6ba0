import request from '@/utils/request'

// 查询APP用户地址列表
export function listAddress(query) {
  return request({
    url: '/fuguang/address/list',
    method: 'get',
    params: query
  })
}

// 查询APP用户地址详细
export function getAddress(addressId) {
  return request({
    url: '/fuguang/address/' + addressId,
    method: 'get'
  })
}

// 新增APP用户地址
export function addAddress(data) {
  return request({
    url: '/fuguang/address',
    method: 'post',
    data: data
  })
}

// 修改APP用户地址
export function updateAddress(data) {
  return request({
    url: '/fuguang/address',
    method: 'put',
    data: data
  })
}

// 删除APP用户地址
export function delAddress(addressId) {
  return request({
    url: '/fuguang/address/' + addressId,
    method: 'delete'
  })
}

// 设置默认地址
export function setDefaultAddress(data) {
  return request({
    url: '/fuguang/address/setDefault',
    method: 'put',
    data: data
  })
}

// 状态修改
export function changeAddressStatus(addressId, status) {
  const data = {
    addressId,
    status
  }
  return request({
    url: '/fuguang/address/changeStatus',
    method: 'put',
    data: data
  })
}

// 根据用户ID查询地址列表
export function getAddressByUserId(userId) {
  return request({
    url: '/fuguang/address/user/' + userId,
    method: 'get'
  })
}

// 查询用户默认地址
export function getDefaultAddress(userId) {
  return request({
    url: '/fuguang/address/default/' + userId,
    method: 'get'
  })
}
