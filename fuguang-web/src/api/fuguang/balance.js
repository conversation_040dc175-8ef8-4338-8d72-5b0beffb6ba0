import request from '@/utils/request'

// 查询用户余额列表
export function listUserBalance(query) {
  return request({
    url: '/fuguang/balance/list',
    method: 'get',
    params: query
  })
}

// 查询用户余额详细
export function getUserBalance(balanceId) {
  return request({
    url: '/fuguang/balance/' + balanceId,
    method: 'get'
  })
}

// 根据用户ID查询用户余额详细
export function getUserBalanceByUserId(userId) {
  return request({
    url: '/fuguang/balance/user/' + userId,
    method: 'get'
  })
}

// 新增用户余额
export function addUserBalance(data) {
  return request({
    url: '/fuguang/balance',
    method: 'post',
    data: data
  })
}

// 修改用户余额
export function updateUserBalance(data) {
  return request({
    url: '/fuguang/balance',
    method: 'put',
    data: data
  })
}

// 删除用户余额
export function delUserBalance(balanceId) {
  return request({
    url: '/fuguang/balance/' + balanceId,
    method: 'delete'
  })
}

// 导出用户余额
export function exportUserBalance(query) {
  return request({
    url: '/fuguang/balance/export',
    method: 'post',
    params: query
  })
}

// 手动调整用户余额
export function adjustBalance(data) {
  return request({
    url: '/fuguang/balance/adjust',
    method: 'post',
    params: data
  })
}

// 冻结用户余额
export function freezeBalance(data) {
  return request({
    url: '/fuguang/balance/freeze',
    method: 'post',
    params: data
  })
}

// 解冻用户余额
export function unfreezeBalance(data) {
  return request({
    url: '/fuguang/balance/unfreeze',
    method: 'post',
    params: data
  })
}

// 获取用户余额统计信息
export function getBalanceStatistics() {
  return request({
    url: '/fuguang/balance/statistics',
    method: 'get'
  })
}
