import request from '@/utils/request'

// 查询APP用户履历时间线列表
export function listTimeline(query) {
  return request({
    url: '/fuguang/timeline/list',
    method: 'get',
    params: query
  })
}

// 根据用户ID查询履历时间线
export function getTimelineByUserId(userId) {
  return request({
    url: '/fuguang/timeline/user/' + userId,
    method: 'get'
  })
}

// 查询APP用户履历时间线详细
export function getTimeline(timelineId) {
  return request({
    url: '/fuguang/timeline/' + timelineId,
    method: 'get'
  })
}

// 新增APP用户履历时间线
export function addTimeline(data) {
  return request({
    url: '/fuguang/timeline',
    method: 'post',
    data: data
  })
}

// 修改APP用户履历时间线
export function updateTimeline(data) {
  return request({
    url: '/fuguang/timeline',
    method: 'put',
    data: data
  })
}

// 删除APP用户履历时间线
export function delTimeline(timelineId) {
  return request({
    url: '/fuguang/timeline/' + timelineId,
    method: 'delete'
  })
}
