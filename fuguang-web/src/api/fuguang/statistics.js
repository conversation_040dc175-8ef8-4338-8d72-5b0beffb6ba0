import request from '@/utils/request'

// 获取平台佣金系统总览
export function getSystemOverview() {
  return request({
    url: '/fuguang/statistics/overview',
    method: 'get'
  })
}

// 获取平台收支趋势（最近12个月）
export function getRevenueTrend() {
  return request({
    url: '/fuguang/statistics/trend',
    method: 'get'
  })
}

// 获取用户余额分布统计
export function getBalanceDistribution() {
  return request({
    url: '/fuguang/statistics/balance-distribution',
    method: 'get'
  })
}

// 获取收入类型分布统计
export function getIncomeDistribution(params) {
  return request({
    url: '/fuguang/statistics/income-distribution',
    method: 'get',
    params: params
  })
}

// 获取提现渠道分布统计
export function getWithdrawChannelDistribution() {
  return request({
    url: '/fuguang/statistics/withdraw-channel',
    method: 'get'
  })
}
