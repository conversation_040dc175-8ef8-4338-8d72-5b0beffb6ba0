import request from '@/utils/request'

// 查询任务评价列表
export function listEvaluation(query) {
  return request({
    url: '/fuguang/evaluation/list',
    method: 'get',
    params: query
  })
}

// 查询任务评价详细
export function getEvaluation(evaluationId) {
  return request({
    url: '/fuguang/evaluation/' + evaluationId,
    method: 'get'
  })
}

// 新增任务评价
export function addEvaluation(data) {
  return request({
    url: '/fuguang/evaluation',
    method: 'post',
    data: data
  })
}

// 修改任务评价
export function updateEvaluation(data) {
  return request({
    url: '/fuguang/evaluation',
    method: 'put',
    data: data
  })
}

// 删除任务评价
export function delEvaluation(evaluationId) {
  return request({
    url: '/fuguang/evaluation/' + evaluationId,
    method: 'delete'
  })
}

// 根据任务ID查询评价
export function getEvaluationByTaskId(taskId) {
  return request({
    url: '/fuguang/evaluation/task/' + taskId,
    method: 'get'
  })
}

// 根据接单人ID查询评价列表
export function getEvaluationByReceiverId(receiverId) {
  return request({
    url: '/fuguang/evaluation/receiver/' + receiverId,
    method: 'get'
  })
}

// 根据发单人ID查询评价列表
export function getEvaluationByPublisherId(publisherId) {
  return request({
    url: '/fuguang/evaluation/publisher/' + publisherId,
    method: 'get'
  })
}

// 获取用户评价统计信息
export function getEvaluationStatistics(receiverId) {
  return request({
    url: '/fuguang/evaluation/statistics/' + receiverId,
    method: 'get'
  })
}

// 检查任务是否可以评价
export function canEvaluateTask(taskId, publisherId) {
  return request({
    url: '/fuguang/evaluation/canEvaluate/' + taskId + '/' + publisherId,
    method: 'get'
  })
}

// 导出任务评价列表
export function exportEvaluation(query) {
  return request({
    url: '/fuguang/evaluation/export',
    method: 'post',
    params: query
  })
}
