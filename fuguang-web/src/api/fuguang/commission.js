import request from '@/utils/request'

// 查询佣金账单列表
export function listCommissionBill(query) {
  return request({
    url: '/fuguang/commission/list',
    method: 'get',
    params: query
  })
}

// 查询佣金账单详细
export function getCommissionBill(billId) {
  return request({
    url: '/fuguang/commission/' + billId,
    method: 'get'
  })
}

// 根据用户ID查询佣金账单列表
export function getCommissionBillsByUserId(userId) {
  return request({
    url: '/fuguang/commission/user/' + userId,
    method: 'get'
  })
}

// 获取指定用户和月份的账单详情
export function getBillByUserIdAndMonth(userId, year, month) {
  return request({
    url: `/fuguang/commission/user/${userId}/${year}/${month}`,
    method: 'get'
  })
}

// 新增佣金账单
export function addCommissionBill(data) {
  return request({
    url: '/fuguang/commission',
    method: 'post',
    data: data
  })
}

// 修改佣金账单
export function updateCommissionBill(data) {
  return request({
    url: '/fuguang/commission',
    method: 'put',
    data: data
  })
}

// 删除佣金账单
export function delCommissionBill(billId) {
  return request({
    url: '/fuguang/commission/' + billId,
    method: 'delete'
  })
}

// 导出佣金账单
export function exportCommissionBill(query) {
  return request({
    url: '/fuguang/commission/export',
    method: 'post',
    params: query
  })
}

// 获取平台佣金统计概览
export function getCommissionOverview(params) {
  return request({
    url: '/fuguang/commission/overview',
    method: 'get',
    params: params
  })
}

// 获取佣金趋势统计（最近12个月）
export function getCommissionTrend() {
  return request({
    url: '/fuguang/commission/trend',
    method: 'get'
  })
}

// 获取收入类型分布统计
export function getIncomeDistribution(params) {
  return request({
    url: '/fuguang/commission/income-distribution',
    method: 'get',
    params: params
  })
}
