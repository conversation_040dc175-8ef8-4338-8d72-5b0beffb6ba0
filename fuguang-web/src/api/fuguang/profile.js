import request from '@/utils/request'

// 查询APP用户个人简介列表
export function listProfile(query) {
  return request({
    url: '/fuguang/profile/list',
    method: 'get',
    params: query
  })
}

// 查询APP用户个人简介详细
export function getProfile(profileId) {
  return request({
    url: '/fuguang/profile/' + profileId,
    method: 'get'
  })
}

// 根据用户ID查询APP用户个人简介详细
export function getProfileByUserId(userId) {
  return request({
    url: '/fuguang/profile/user/' + userId,
    method: 'get'
  })
}

// 新增APP用户个人简介
export function addProfile(data) {
  return request({
    url: '/fuguang/profile',
    method: 'post',
    data: data
  })
}

// 修改APP用户个人简介
export function updateProfile(data) {
  return request({
    url: '/fuguang/profile',
    method: 'put',
    data: data
  })
}

// 删除APP用户个人简介
export function delProfile(profileId) {
  return request({
    url: '/fuguang/profile/' + profileId,
    method: 'delete'
  })
}

// 更新用户信用分
export function updateCreditScore(userId, score) {
  return request({
    url: '/fuguang/profile/creditScore/' + userId + '/' + score,
    method: 'put'
  })
}

// 更新用户任务分
export function updateTaskScore(userId, score) {
  return request({
    url: '/fuguang/profile/taskScore/' + userId + '/' + score,
    method: 'put'
  })
}

// 设置扶贫救援徽章
export function updatePovertyBadge(userId, badge) {
  return request({
    url: '/fuguang/profile/povertyBadge/' + userId + '/' + badge,
    method: 'put'
  })
}

// 初始化用户个人简介
export function initUserProfile(userId) {
  return request({
    url: '/fuguang/profile/init/' + userId,
    method: 'post'
  })
}
