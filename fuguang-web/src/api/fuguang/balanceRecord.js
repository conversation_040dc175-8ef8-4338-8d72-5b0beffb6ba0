import request from '@/utils/request'

// 查询余额变动记录列表
export function listBalanceRecord(query) {
  return request({
    url: '/fuguang/balanceRecord/list',
    method: 'get',
    params: query
  })
}

// 查询余额变动记录详细
export function getBalanceRecord(recordId) {
  return request({
    url: '/fuguang/balanceRecord/' + recordId,
    method: 'get'
  })
}

// 根据用户ID查询余额变动记录
export function getBalanceRecordsByUserId(userId) {
  return request({
    url: '/fuguang/balanceRecord/user/' + userId,
    method: 'get'
  })
}

// 根据用户ID和时间范围查询余额变动记录
export function getBalanceRecordsByUserIdAndTime(userId, params) {
  return request({
    url: `/fuguang/balanceRecord/user/${userId}/time`,
    method: 'get',
    params: params
  })
}

// 新增余额变动记录
export function addBalanceRecord(data) {
  return request({
    url: '/fuguang/balanceRecord',
    method: 'post',
    data: data
  })
}

// 修改余额变动记录
export function updateBalanceRecord(data) {
  return request({
    url: '/fuguang/balanceRecord',
    method: 'put',
    data: data
  })
}

// 删除余额变动记录
export function delBalanceRecord(recordId) {
  return request({
    url: '/fuguang/balanceRecord/' + recordId,
    method: 'delete'
  })
}

// 导出余额变动记录
export function exportBalanceRecord(query) {
  return request({
    url: '/fuguang/balanceRecord/export',
    method: 'post',
    params: query
  })
}

// 获取余额变动统计信息
export function getBalanceRecordStatistics(params) {
  return request({
    url: '/fuguang/balanceRecord/statistics',
    method: 'get',
    params: params
  })
}

// 获取收入类型分布统计
export function getIncomeDistribution(params) {
  return request({
    url: '/fuguang/balanceRecord/income-distribution',
    method: 'get',
    params: params
  })
}

// 获取业务类型分布统计
export function getBusinessDistribution(params) {
  return request({
    url: '/fuguang/balanceRecord/business-distribution',
    method: 'get',
    params: params
  })
}
