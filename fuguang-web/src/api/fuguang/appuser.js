import request from '@/utils/request'

// 查询APP用户列表
export function listAppUser(query) {
  return request({
    url: '/fuguang/appuser/list',
    method: 'get',
    params: query
  })
}

// 查询APP用户详细
export function getAppUser(userId) {
  return request({
    url: '/fuguang/appuser/' + userId,
    method: 'get'
  })
}

// 新增APP用户
export function addAppUser(data) {
  return request({
    url: '/fuguang/appuser',
    method: 'post',
    data: data
  })
}

// 修改APP用户
export function updateAppUser(data) {
  return request({
    url: '/fuguang/appuser',
    method: 'put',
    data: data
  })
}

// 删除APP用户
export function delAppUser(userId) {
  return request({
    url: '/fuguang/appuser/' + userId,
    method: 'delete'
  })
}

// 重置密码
export function resetAppUserPwd(data) {
  return request({
    url: '/fuguang/appuser/resetPwd',
    method: 'put',
    data: data
  })
}

// 状态修改
export function changeAppUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/fuguang/appuser/changeStatus',
    method: 'put',
    data: data
  })
}
