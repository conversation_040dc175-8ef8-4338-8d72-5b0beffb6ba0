import request from '@/utils/request'

// 查询商家申请列表
export function listMerchantApplication(query) {
  return request({
    url: '/fuguang/merchant/list',
    method: 'get',
    params: query
  })
}

// 查询商家申请详细
export function getMerchantApplication(applicationId) {
  return request({
    url: '/fuguang/merchant/' + applicationId,
    method: 'get'
  })
}

// 新增商家申请
export function addMerchantApplication(data) {
  return request({
    url: '/fuguang/merchant',
    method: 'post',
    data: data
  })
}

// 修改商家申请
export function updateMerchantApplication(data) {
  return request({
    url: '/fuguang/merchant',
    method: 'put',
    data: data
  })
}

// 删除商家申请
export function delMerchantApplication(applicationId) {
  return request({
    url: '/fuguang/merchant/' + applicationId,
    method: 'delete'
  })
}

// 审核商家申请
export function auditMerchantApplication(data) {
  return request({
    url: '/fuguang/merchant/audit',
    method: 'put',
    data: data
  })
}

// 批量审核商家申请
export function batchAuditMerchantApplication(data) {
  return request({
    url: '/fuguang/merchant/batchAudit',
    method: 'put',
    data: data
  })
}

// 获取待审核申请数量
export function getPendingCount() {
  return request({
    url: '/fuguang/merchant/pendingCount',
    method: 'get'
  })
}
