import request from '@/utils/request'

// 查询APP功能配置列表
export function listAppFunction(query) {
  return request({
    url: '/fuguang/function/list',
    method: 'get',
    params: query
  })
}

// 查询APP功能配置详细
export function getAppFunction(functionId) {
  return request({
    url: '/fuguang/function/' + functionId,
    method: 'get'
  })
}

// 新增APP功能配置
export function addAppFunction(data) {
  return request({
    url: '/fuguang/function',
    method: 'post',
    data: data
  })
}

// 修改APP功能配置
export function updateAppFunction(data) {
  return request({
    url: '/fuguang/function',
    method: 'put',
    data: data
  })
}

// 删除APP功能配置
export function delAppFunction(functionId) {
  return request({
    url: '/fuguang/function/' + functionId,
    method: 'delete'
  })
}

// 获取启用的APP功能配置列表
export function getEnabledFunctions() {
  return request({
    url: '/fuguang/function/enabled',
    method: 'get'
  })
}

// 修改功能状态
export function changeStatus(data) {
  return request({
    url: '/fuguang/function/changeStatus',
    method: 'put',
    data: data
  })
}
