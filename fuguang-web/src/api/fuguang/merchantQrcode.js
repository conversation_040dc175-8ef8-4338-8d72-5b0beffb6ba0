import request from '@/utils/request'

// 查询商家二维码列表
export function listMerchantQrcode(query) {
  return request({
    url: '/fuguang/merchantQrcode/list',
    method: 'get',
    params: query
  })
}

// 查询商家二维码详细
export function getMerchantQrcode(qrcodeId) {
  return request({
    url: '/fuguang/merchantQrcode/' + qrcodeId,
    method: 'get'
  })
}

// 新增商家二维码
export function addMerchantQrcode(data) {
  return request({
    url: '/fuguang/merchantQrcode',
    method: 'post',
    data: data
  })
}

// 修改商家二维码
export function updateMerchantQrcode(data) {
  return request({
    url: '/fuguang/merchantQrcode',
    method: 'put',
    data: data
  })
}

// 删除商家二维码
export function delMerchantQrcode(qrcodeId) {
  return request({
    url: '/fuguang/merchantQrcode/' + qrcodeId,
    method: 'delete'
  })
}

// 启用/禁用商家二维码
export function changeStatus(qrcodeId, status) {
  const data = {
    qrcodeId,
    status
  }
  return request({
    url: '/fuguang/merchantQrcode/changeStatus',
    method: 'put',
    data: data
  })
}

// 为商家生成二维码
export function generateQrcode(merchantId) {
  return request({
    url: '/fuguang/merchantQrcode/generate/' + merchantId,
    method: 'post'
  })
}

// 根据商家ID查询二维码
export function getMerchantQrcodeByMerchantId(merchantId) {
  return request({
    url: '/fuguang/merchantQrcode/merchant/' + merchantId,
    method: 'get'
  })
}

// 批量启用/禁用二维码
export function batchChangeStatus(qrcodeIds, status) {
  const data = {
    qrcodeIds,
    status
  }
  return request({
    url: '/fuguang/merchantQrcode/batchChangeStatus',
    method: 'put',
    data: data
  })
}

// 获取二维码统计信息
export function getMerchantQrcodeStatistics() {
  return request({
    url: '/fuguang/merchantQrcode/statistics',
    method: 'get'
  })
}
