import request from '@/utils/request'

// 查询提现记录列表
export function listWithdrawRecord(query) {
  return request({
    url: '/fuguang/withdraw/list',
    method: 'get',
    params: query
  })
}

// 查询提现记录详细
export function getWithdrawRecord(withdrawId) {
  return request({
    url: '/fuguang/withdraw/' + withdrawId,
    method: 'get'
  })
}

// 根据用户ID查询提现记录
export function getWithdrawRecordsByUserId(userId) {
  return request({
    url: '/fuguang/withdraw/user/' + userId,
    method: 'get'
  })
}

// 导出提现记录
export function exportWithdrawRecord(query) {
  return request({
    url: '/fuguang/withdraw/export',
    method: 'post',
    params: query
  })
}

// 审核提现申请
export function auditWithdraw(data) {
  return request({
    url: '/fuguang/withdraw/audit',
    method: 'post',
    params: data
  })
}

// 批量审核提现申请
export function batchAuditWithdraw(data) {
  return request({
    url: '/fuguang/withdraw/batchAudit',
    method: 'post',
    params: data
  })
}

// 获取待审核提现数量
export function getPendingCount() {
  return request({
    url: '/fuguang/withdraw/pendingCount',
    method: 'get'
  })
}

// 获取提现统计信息
export function getWithdrawStatistics(params) {
  return request({
    url: '/fuguang/withdraw/statistics',
    method: 'get',
    params: params
  })
}
