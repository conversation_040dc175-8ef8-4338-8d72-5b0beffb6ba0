import request from '@/utils/request'

// 查询APP任务类型列表
export function listTaskType(query) {
  return request({
    url: '/fuguang/taskType/list',
    method: 'get',
    params: query
  })
}

// 查询APP任务类型详细
export function getTaskType(typeId) {
  return request({
    url: '/fuguang/taskType/' + typeId,
    method: 'get'
  })
}

// 新增APP任务类型
export function addTaskType(data) {
  return request({
    url: '/fuguang/taskType',
    method: 'post',
    data: data
  })
}

// 修改APP任务类型
export function updateTaskType(data) {
  return request({
    url: '/fuguang/taskType',
    method: 'put',
    data: data
  })
}

// 删除APP任务类型
export function delTaskType(typeId) {
  return request({
    url: '/fuguang/taskType/' + typeId,
    method: 'delete'
  })
}

// 获取一级任务类型列表
export function getFirstLevelTypes() {
  return request({
    url: '/fuguang/taskType/firstLevel',
    method: 'get'
  })
}

// 根据父类型ID获取子类型列表
export function getChildrenTypes(parentId) {
  return request({
    url: '/fuguang/taskType/children/' + parentId,
    method: 'get'
  })
}
