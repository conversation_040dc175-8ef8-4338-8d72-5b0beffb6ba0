import request from '@/utils/request'

// 查询商品信息列表
export function listProduct(query) {
  return request({
    url: '/mall/product/list',
    method: 'get',
    params: query
  })
}

// 查询商品信息详细
export function getProduct(productId) {
  return request({
    url: '/mall/product/' + productId,
    method: 'get'
  })
}

// 新增商品信息
export function addProduct(data) {
  return request({
    url: '/mall/product',
    method: 'post',
    data: data
  })
}

// 修改商品信息
export function updateProduct(data) {
  return request({
    url: '/mall/product',
    method: 'put',
    data: data
  })
}

// 删除商品信息
export function delProduct(productId) {
  return request({
    url: '/mall/product/' + productId,
    method: 'delete'
  })
}

// 上架商品
export function onShelfProduct(productId) {
  return request({
    url: '/mall/product/onShelf/' + productId,
    method: 'put'
  })
}

// 下架商品
export function offShelfProduct(productId) {
  return request({
    url: '/mall/product/offShelf/' + productId,
    method: 'put'
  })
}

// 批量上架商品
export function batchOnShelfProduct(productIds) {
  return request({
    url: '/mall/product/batchOnShelf',
    method: 'put',
    data: productIds
  })
}

// 批量下架商品
export function batchOffShelfProduct(productIds) {
  return request({
    url: '/mall/product/batchOffShelf',
    method: 'put',
    data: productIds
  })
}
