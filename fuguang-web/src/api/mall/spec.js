import request from '@/utils/request'

// 查询商品规格列表
export function listSpec(query) {
  return request({
    url: '/mall/spec/list',
    method: 'get',
    params: query
  })
}

// 查询商品规格详细
export function getSpec(specId) {
  return request({
    url: '/mall/spec/' + specId,
    method: 'get'
  })
}

// 根据商品ID查询规格列表
export function listSpecByProduct(productId) {
  return request({
    url: '/mall/spec/listByProduct/' + productId,
    method: 'get'
  })
}

// 新增商品规格
export function addSpec(data) {
  return request({
    url: '/mall/spec',
    method: 'post',
    data: data
  })
}

// 修改商品规格
export function updateSpec(data) {
  return request({
    url: '/mall/spec',
    method: 'put',
    data: data
  })
}

// 删除商品规格
export function delSpec(specId) {
  return request({
    url: '/mall/spec/' + specId,
    method: 'delete'
  })
}

// 上架规格
export function onShelfSpec(specId) {
  return request({
    url: '/mall/spec/onShelf/' + specId,
    method: 'put'
  })
}

// 下架规格
export function offShelfSpec(specId) {
  return request({
    url: '/mall/spec/offShelf/' + specId,
    method: 'put'
  })
}
