import request from '@/utils/request'

// 查询订单物流列表
export function listLogistics(query) {
  return request({
    url: '/mall/logistics/list',
    method: 'get',
    params: query
  })
}

// 查询订单物流详细
export function getLogistics(logisticsId) {
  return request({
    url: '/mall/logistics/' + logisticsId,
    method: 'get'
  })
}

// 根据订单ID查询物流列表
export function listLogisticsByOrder(orderId) {
  return request({
    url: '/mall/logistics/listByOrder/' + orderId,
    method: 'get'
  })
}

// 新增订单物流
export function addLogistics(data) {
  return request({
    url: '/mall/logistics',
    method: 'post',
    data: data
  })
}

// 修改订单物流
export function updateLogistics(data) {
  return request({
    url: '/mall/logistics',
    method: 'put',
    data: data
  })
}

// 删除订单物流
export function delLogistics(logisticsId) {
  return request({
    url: '/mall/logistics/' + logisticsId,
    method: 'delete'
  })
}

// 创建发货记录
export function createDelivery(data) {
  return request({
    url: '/mall/logistics/delivery',
    method: 'post',
    data: data
  })
}

// 更新物流状态
export function updateLogisticsStatus(data) {
  return request({
    url: '/mall/logistics/status',
    method: 'put',
    data: data
  })
}
