<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商家名称" prop="merchantName">
        <el-input v-model="queryParams.merchantName" placeholder="请输入商家名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select v-model="queryParams.payStatus" placeholder="请选择支付状态" clearable>
          <el-option v-for="dict in dict.type.pay_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="转账状态" prop="transferStatus">
        <el-select v-model="queryParams.transferStatus" placeholder="请选择转账状态" clearable>
          <el-option v-for="dict in dict.type.transfer_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['fuguang:offlinePayment:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['fuguang:offlinePayment:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['fuguang:offlinePayment:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['fuguang:offlinePayment:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-refresh" size="mini" @click="handleBatchRetryTransfer"
          :disabled="multiple" v-hasPermi="['fuguang:offlinePayment:transfer']">批量重试转账</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="offlinePaymentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="支付记录ID" align="center" prop="paymentId" width="100" />
      <el-table-column label="商家名称" align="center" prop="merchantName" width="120" />
      <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
      <el-table-column label="支付金额" align="center" prop="payAmount" width="100">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">￥{{ scope.row.payAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payType" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.pay_type" :value="scope.row.payType" />
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="payStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.pay_status" :value="scope.row.payStatus" />
        </template>
      </el-table-column>
      <el-table-column label="转账状态" align="center" prop="transferStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.transfer_status" :value="scope.row.transferStatus" />
        </template>
      </el-table-column>
      <el-table-column label="转账金额" align="center" prop="transferAmount" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.transferAmount" style="color: #67C23A; font-weight: bold;">￥{{ scope.row.transferAmount
          }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="平台手续费" align="center" prop="platformFee" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.platformFee" style="color: #F56C6C;">￥{{ scope.row.platformFee }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="payTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.payTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['fuguang:offlinePayment:query']">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:offlinePayment:edit']">修改</el-button>
          <el-button v-if="scope.row.payStatus === '1' && scope.row.transferStatus === '3'" size="mini" type="text"
            icon="el-icon-refresh" @click="handleRetryTransfer(scope.row)"
            v-hasPermi="['fuguang:offlinePayment:transfer']" style="color: #E6A23C;">重新转账</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:offlinePayment:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改线下支付订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商家ID" prop="merchantId">
          <el-input v-model="form.merchantId" placeholder="请输入商家ID" />
        </el-form-item>
        <el-form-item label="商家名称" prop="merchantName">
          <el-input v-model="form.merchantName" placeholder="请输入商家名称" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入支付订单号" />
        </el-form-item>
        <el-form-item label="支付金额" prop="payAmount">
          <el-input v-model="form.payAmount" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-select v-model="form.payType" placeholder="请选择支付方式">
            <el-option v-for="dict in dict.type.pay_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态" prop="payStatus">
          <el-select v-model="form.payStatus" placeholder="请选择支付状态">
            <el-option v-for="dict in dict.type.pay_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="支付订单详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-form ref="viewForm" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="支付记录ID">
              <span>{{ form.paymentId }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商家名称">
              <span>{{ form.merchantName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单号">
              <span>{{ form.orderNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付金额">
              <span style="color: #E6A23C; font-weight: bold;">￥{{ form.payAmount }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="支付方式">
              <dict-tag :options="dict.type.pay_type" :value="form.payType" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付状态">
              <dict-tag :options="dict.type.pay_status" :value="form.payStatus" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="转账状态">
              <dict-tag :options="dict.type.transfer_status" :value="form.transferStatus" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="第三方交易号">
              <span>{{ form.tradeNo || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="转账金额">
              <span v-if="form.transferAmount" style="color: #67C23A; font-weight: bold;">￥{{ form.transferAmount
              }}</span>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台手续费">
              <span v-if="form.platformFee" style="color: #F56C6C;">￥{{ form.platformFee }}</span>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="支付时间">
              <span>{{ parseTime(form.payTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="转账时间">
              <span>{{ parseTime(form.transferTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建时间">
              <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新时间">
              <span>{{ parseTime(form.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.remark">
          <el-col :span="24">
            <el-form-item label="备注">
              <span>{{ form.remark }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOfflinePayment, getOfflinePayment, delOfflinePayment, addOfflinePayment, updateOfflinePayment, retryTransfer } from "@/api/fuguang/offlinePayment"

export default {
  name: "OfflinePayment",
  dicts: ['pay_status', 'pay_type', 'transfer_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 线下支付订单表格数据
      offlinePaymentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        merchantName: null,
        orderNo: null,
        payStatus: null,
        transferStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        merchantId: [
          { required: true, message: "商家ID不能为空", trigger: "blur" }
        ],
        merchantName: [
          { required: true, message: "商家名称不能为空", trigger: "blur" }
        ],
        orderNo: [
          { required: true, message: "支付订单号不能为空", trigger: "blur" }
        ],
        payAmount: [
          { required: true, message: "支付金额不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询线下支付订单列表 */
    getList() {
      this.loading = true;
      listOfflinePayment(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.offlinePaymentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        paymentId: null,
        merchantId: null,
        merchantName: null,
        orderNo: null,
        payAmount: null,
        payType: null,
        payStatus: null,
        tradeNo: null,
        payTime: null,
        notifyTime: null,
        transferStatus: null,
        transferNo: null,
        transferTime: null,
        transferAmount: null,
        platformFee: null,
        createTime: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.paymentId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加线下支付订单";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const paymentId = row.paymentId || this.ids
      getOfflinePayment(paymentId).then(response => {
        this.form = response.data;
        this.viewOpen = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const paymentId = row.paymentId || this.ids
      getOfflinePayment(paymentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改线下支付订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.paymentId != null) {
            updateOfflinePayment(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOfflinePayment(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const paymentIds = row.paymentId || this.ids;
      this.$modal.confirm('是否确认删除线下支付订单编号为"' + paymentIds + '"的数据项？').then(function () {
        return delOfflinePayment(paymentIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/offlinePayment/export', {
        ...this.queryParams
      }, `offlinePayment_${new Date().getTime()}.xlsx`)
    },
    /** 重新转账操作 */
    handleRetryTransfer(row) {
      this.$modal.confirm('是否确认重新转账给商家"' + row.merchantName + '"？').then(function () {
        return retryTransfer(row.paymentId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("转账操作已提交");
      }).catch(() => { });
    },
    /** 批量重试转账操作 */
    handleBatchRetryTransfer() {
      const paymentIds = this.ids;
      this.$modal.confirm('是否确认批量重试转账？').then(() => {
        let successCount = 0;
        let failCount = 0;
        const promises = paymentIds.map(paymentId => {
          return retryTransfer(paymentId).then(() => {
            successCount++;
          }).catch(() => {
            failCount++;
          });
        });

        Promise.all(promises).then(() => {
          this.getList();
          this.$modal.msgSuccess(`批量转账操作完成，成功：${successCount}，失败：${failCount}`);
        });
      }).catch(() => { });
    }
  }
};
</script>
