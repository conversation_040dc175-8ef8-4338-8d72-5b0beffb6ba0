<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务标题" prop="taskTitle">
        <el-input
          v-model="queryParams.taskTitle"
          placeholder="请输入任务标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发单人" prop="publisherName">
        <el-input
          v-model="queryParams.publisherName"
          placeholder="请输入发单人昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接单人" prop="receiverName">
        <el-input
          v-model="queryParams.receiverName"
          placeholder="请输入接单人昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评分" prop="rating">
        <el-select v-model="queryParams.rating" placeholder="请选择评分" clearable>
          <el-option label="1分" value="1" />
          <el-option label="2分" value="2" />
          <el-option label="3分" value="3" />
          <el-option label="4分" value="4" />
          <el-option label="5分" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="隐藏" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fuguang:evaluation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['fuguang:evaluation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="evaluationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="评价ID" align="center" prop="evaluationId" width="80" />
      <el-table-column label="任务标题" align="center" prop="taskTitle" :show-overflow-tooltip="true" />
      <el-table-column label="发单人" align="center" prop="publisherName" width="120" />
      <el-table-column label="接单人" align="center" prop="receiverName" width="120" />
      <el-table-column label="评分" align="center" prop="rating" width="100">
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}分">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column label="评价内容" align="center" prop="evaluationContent" :show-overflow-tooltip="true" />
      <el-table-column label="匿名评价" align="center" prop="isAnonymous" width="100">
        <template slot-scope="scope">
          <dict-tag :options="[{label: '否', value: '0'}, {label: '是', value: '1'}]" :value="scope.row.isAnonymous"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="[{label: '正常', value: '0'}, {label: '隐藏', value: '1'}]" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="评价时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['fuguang:evaluation:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:evaluation:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:evaluation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 评价详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务标题：">
              <span>{{ form.taskTitle }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评分：">
              <el-rate
                v-model="form.rating"
                :disabled="!isEdit"
                show-score
                text-color="#ff9900"
                score-template="{value}分">
              </el-rate>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发单人：">
              <span>{{ form.publisherName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接单人：">
              <span>{{ form.receiverName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="匿名评价：">
              <el-radio-group v-model="form.isAnonymous" :disabled="!isEdit">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态：">
              <el-radio-group v-model="form.status" :disabled="!isEdit">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">隐藏</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="评价内容：">
              <el-input
                v-model="form.evaluationContent"
                type="textarea"
                :rows="4"
                placeholder="请输入评价内容"
                :disabled="!isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="评价标签：">
              <el-input
                v-model="form.evaluationTags"
                placeholder="请输入评价标签（JSON格式）"
                :disabled="!isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注：">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入备注"
                :disabled="!isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="isEdit">确 定</el-button>
        <el-button @click="cancel">{{ isEdit ? '取 消' : '关 闭' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEvaluation, getEvaluation, delEvaluation, updateEvaluation, exportEvaluation } from "@/api/fuguang/evaluation";

export default {
  name: "Evaluation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务评价表格数据
      evaluationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否编辑模式
      isEdit: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskTitle: null,
        publisherName: null,
        receiverName: null,
        rating: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        rating: [
          { required: true, message: "评分不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询任务评价列表 */
    getList() {
      this.loading = true;
      listEvaluation(this.queryParams).then(response => {
        this.evaluationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        evaluationId: null,
        taskId: null,
        taskTitle: null,
        publisherId: null,
        publisherName: null,
        receiverId: null,
        receiverName: null,
        rating: null,
        evaluationContent: null,
        evaluationTags: null,
        isAnonymous: "0",
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.evaluationId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const evaluationId = row.evaluationId || this.ids
      getEvaluation(evaluationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.isEdit = false;
        this.title = "查看任务评价";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const evaluationId = row.evaluationId || this.ids
      getEvaluation(evaluationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.isEdit = true;
        this.title = "修改任务评价";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateEvaluation(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const evaluationIds = row.evaluationId || this.ids;
      this.$modal.confirm('是否确认删除任务评价编号为"' + evaluationIds + '"的数据项？').then(function () {
        return delEvaluation(evaluationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/evaluation/export', {
        ...this.queryParams
      }, `evaluation_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
