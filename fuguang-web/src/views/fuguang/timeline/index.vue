<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事件类型" prop="eventType">
        <el-select v-model="queryParams.eventType" placeholder="请选择事件类型" clearable>
          <el-option label="注册" value="register" />
          <el-option label="完成任务" value="task_complete" />
          <el-option label="获得奖励" value="reward" />
          <el-option label="等级提升" value="level_up" />
        </el-select>
      </el-form-item>
      <el-form-item label="事件标题" prop="eventTitle">
        <el-input
          v-model="queryParams.eventTitle"
          placeholder="请输入事件标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['fuguang:timeline:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['fuguang:timeline:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fuguang:timeline:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['fuguang:timeline:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="timelineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="时间线ID" align="center" prop="timelineId" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="事件类型" align="center" prop="eventType">
        <template slot-scope="scope">
          <el-tag :type="getEventTypeColor(scope.row.eventType)">{{ getEventTypeName(scope.row.eventType) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="事件标题" align="center" prop="eventTitle" :show-overflow-tooltip="true" />
      <el-table-column label="事件描述" align="center" prop="eventDesc" :show-overflow-tooltip="true" />
      <el-table-column label="事件时间" align="center" prop="eventTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.eventTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0'" type="success">正常</el-tag>
          <el-tag v-else type="info">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:timeline:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:timeline:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改履历时间线对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="事件类型" prop="eventType">
          <el-select v-model="form.eventType" placeholder="请选择事件类型">
            <el-option label="注册" value="register" />
            <el-option label="完成任务" value="task_complete" />
            <el-option label="获得奖励" value="reward" />
            <el-option label="等级提升" value="level_up" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="事件标题" prop="eventTitle">
          <el-input v-model="form.eventTitle" placeholder="请输入事件标题" />
        </el-form-item>
        <el-form-item label="事件描述" prop="eventDesc">
          <el-input v-model="form.eventDesc" type="textarea" placeholder="请输入事件描述" />
        </el-form-item>
        <el-form-item label="事件数据" prop="eventData">
          <el-input v-model="form.eventData" type="textarea" placeholder="请输入事件数据(JSON格式)" />
        </el-form-item>
        <el-form-item label="事件时间" prop="eventTime">
          <el-date-picker clearable
            v-model="form.eventTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择事件时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请输入图标" />
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <el-select v-model="form.color" placeholder="请选择颜色">
            <el-option label="主要" value="primary" />
            <el-option label="成功" value="success" />
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="危险" value="danger" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTimeline, getTimeline, delTimeline, addTimeline, updateTimeline } from "@/api/fuguang/timeline";

export default {
  name: "Timeline",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 履历时间线表格数据
      timelineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        eventType: null,
        eventTitle: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        eventType: [
          { required: true, message: "事件类型不能为空", trigger: "change" }
        ],
        eventTitle: [
          { required: true, message: "事件标题不能为空", trigger: "blur" }
        ],
        eventTime: [
          { required: true, message: "事件时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询履历时间线列表 */
    getList() {
      this.loading = true;
      listTimeline(this.queryParams).then(response => {
        this.timelineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        timelineId: null,
        userId: null,
        eventType: null,
        eventTitle: null,
        eventDesc: null,
        eventData: null,
        eventTime: null,
        icon: null,
        color: "primary",
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.timelineId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加履历时间线";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const timelineId = row.timelineId || this.ids
      getTimeline(timelineId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改履历时间线";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.timelineId != null) {
            updateTimeline(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTimeline(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const timelineIds = row.timelineId || this.ids;
      this.$modal.confirm('是否确认删除履历时间线编号为"' + timelineIds + '"的数据项？').then(function() {
        return delTimeline(timelineIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/timeline/export', {
        ...this.queryParams
      }, `timeline_${new Date().getTime()}.xlsx`)
    },
    /** 获取事件类型名称 */
    getEventTypeName(type) {
      const typeMap = {
        'register': '注册',
        'task_complete': '完成任务',
        'reward': '获得奖励',
        'level_up': '等级提升'
      };
      return typeMap[type] || type;
    },
    /** 获取事件类型颜色 */
    getEventTypeColor(type) {
      const colorMap = {
        'register': 'success',
        'task_complete': 'primary',
        'reward': 'warning',
        'level_up': 'danger'
      };
      return colorMap[type] || 'info';
    }
  }
};
</script>
