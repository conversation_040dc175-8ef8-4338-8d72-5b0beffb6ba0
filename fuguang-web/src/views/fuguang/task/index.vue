<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务标题" prop="taskTitle">
        <el-input
          v-model="queryParams.taskTitle"
          placeholder="请输入任务标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布者" prop="publisherName">
        <el-input
          v-model="queryParams.publisherName"
          placeholder="请输入发布者昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
          <el-option label="待接取" value="0" />
          <el-option label="进行中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
          <el-option label="普通任务" value="0" />
          <el-option label="紧急任务" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度" prop="urgentLevel">
        <el-select v-model="queryParams.urgentLevel" placeholder="请选择紧急程度" clearable>
          <el-option label="普通" value="0" />
          <el-option label="紧急" value="1" />
          <el-option label="非常紧急" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fuguang:task:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['fuguang:task:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务ID" align="center" prop="taskId" width="80" />
      <el-table-column label="任务标题" align="center" prop="taskTitle" :show-overflow-tooltip="true" />
      <el-table-column label="任务金额" align="center" prop="taskAmount" width="100">
        <template slot-scope="scope">
          <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.taskAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发布者" align="center" prop="publisherName" width="120" />
      <el-table-column label="接收者" align="center" prop="receiverName" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.receiverName">{{ scope.row.receiverName }}</span>
          <span v-else style="color: #909399;">暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="taskStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.taskStatus === '0'" type="info">待接取</el-tag>
          <el-tag v-else-if="scope.row.taskStatus === '1'" type="warning">进行中</el-tag>
          <el-tag v-else-if="scope.row.taskStatus === '2'" type="success">已完成</el-tag>
          <el-tag v-else-if="scope.row.taskStatus === '3'" type="danger">已取消</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="紧急程度" align="center" prop="urgentLevel" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.urgentLevel === '0'" type="info">普通</el-tag>
          <el-tag v-else-if="scope.row.urgentLevel === '1'" type="warning">紧急</el-tag>
          <el-tag v-else-if="scope.row.urgentLevel === '2'" type="danger">非常紧急</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" prop="viewCount" width="100" />
      <el-table-column label="热度分数" align="center" prop="hotScore" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['fuguang:task:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:task:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 任务详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="small">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务标题：">
              <span>{{ form.taskTitle }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务金额：">
              <span style="color: #f56c6c; font-weight: bold;">¥{{ form.taskAmount }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发布者：">
              <span>{{ form.publisherName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收者：">
              <span>{{ form.receiverName || '暂无' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务状态：">
              <el-tag v-if="form.taskStatus === '0'" type="info">待接取</el-tag>
              <el-tag v-else-if="form.taskStatus === '1'" type="warning">进行中</el-tag>
              <el-tag v-else-if="form.taskStatus === '2'" type="success">已完成</el-tag>
              <el-tag v-else-if="form.taskStatus === '3'" type="danger">已取消</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急程度：">
              <el-tag v-if="form.urgentLevel === '0'" type="info">普通</el-tag>
              <el-tag v-else-if="form.urgentLevel === '1'" type="warning">紧急</el-tag>
              <el-tag v-else-if="form.urgentLevel === '2'" type="danger">非常紧急</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务地址：">
              <span>{{ form.taskAddress }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="任务描述：">
              <div style="max-height: 200px; overflow-y: auto; padding: 10px; border: 1px solid #dcdfe6; border-radius: 4px;">
                {{ form.taskDesc }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间：">
              <span>{{ parseTime(form.startTime, '{y}-{m}-{d} {h}:{i}') || '未设置' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间：">
              <span>{{ parseTime(form.endTime, '{y}-{m}-{d} {h}:{i}') || '未设置' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="浏览次数：">
              <span>{{ form.viewCount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="热度分数：">
              <span>{{ form.hotScore }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建时间：">
              <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新时间：">
              <span>{{ parseTime(form.updateTime, '{y}-{m}-{d} {h}:{i}') || '未更新' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTask, getTask, delTask } from "@/api/fuguang/task";

export default {
  name: "Task",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // APP任务表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskTitle: null,
        publisherName: null,
        taskStatus: null,
        taskType: null,
        urgentLevel: null
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询APP任务列表 */
    getList() {
      this.loading = true;
      listTask(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.taskId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const taskId = row.taskId;
      getTask(taskId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "任务详情";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const taskIds = row.taskId || this.ids;
      this.$modal.confirm('是否确认删除任务编号为"' + taskIds + '"的数据项？').then(function() {
        return delTask(taskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/task/export', {
        ...this.queryParams
      }, `task_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
