<template>
  <div class="app-container" v-loading="loading">
    <!-- 调试信息 -->
    <el-alert v-if="!userId" title="错误" type="error" description="缺少用户ID参数" show-icon></el-alert>

    <el-card class="box-card" v-if="userId">
      <div slot="header" class="clearfix">
        <span>{{ userName }}的个人简介 (ID: {{ userId }})</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <!-- 基本信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never">
            <div slot="header">
              <span>基本信息</span>
              <el-button style="float: right; padding: 3px 0" type="text" @click="handleEdit">编辑</el-button>
            </div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="信用分">
                <el-tag :type="getCreditScoreType(profile.creditScore)">{{ profile.creditScore }}分</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="任务分">
                <el-tag type="primary">{{ profile.taskScore }}分</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="用户等级">
                <el-tag type="warning">{{ profile.level }}级</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="经验值">
                {{ profile.experience }}
              </el-descriptions-item>
              <el-descriptions-item label="扶贫救援徽章">
                <el-tag v-if="profile.povertyReliefBadge === '1'" type="success">已获得</el-tag>
                <el-tag v-else type="info">未获得</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="任务成功率">
                {{ profile.successRate }}%
              </el-descriptions-item>
              <el-descriptions-item label="总任务数">
                {{ profile.totalTasks }}
              </el-descriptions-item>
              <el-descriptions-item label="完成任务数">
                {{ profile.completedTasks }}
              </el-descriptions-item>
              <el-descriptions-item label="总收益" :span="2">
                <el-tag type="danger">¥{{ profile.totalEarnings }}</el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never">
            <div slot="header">
              <span>个人描述</span>
            </div>
            <div class="profile-desc">
              {{ profile.profileDesc || '暂无个人描述' }}
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 履历时间线 -->
      <el-card shadow="never" style="margin-top: 20px;">
        <div slot="header">
          <span>履历时间线</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="refreshTimeline">刷新</el-button>
        </div>
        <el-timeline>
          <el-timeline-item v-for="item in timeline" :key="item.timelineId" :icon="getTimelineIcon(item.icon)"
            :type="item.color" :timestamp="parseTime(item.eventTime, '{y}-{m}-{d} {h}:{i}:{s}')">
            <el-card>
              <h4>{{ item.eventTitle }}</h4>
              <p>{{ item.eventDesc }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog title="编辑个人简介" :visible.sync="editOpen" width="600px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="信用分" prop="creditScore">
          <el-input-number v-model="editForm.creditScore" :min="0" :max="200" />
        </el-form-item>
        <el-form-item label="任务分" prop="taskScore">
          <el-input-number v-model="editForm.taskScore" :min="0" />
        </el-form-item>
        <el-form-item label="扶贫救援徽章" prop="povertyReliefBadge">
          <el-radio-group v-model="editForm.povertyReliefBadge">
            <el-radio label="0">未获得</el-radio>
            <el-radio label="1">已获得</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="个人描述" prop="profileDesc">
          <el-input v-model="editForm.profileDesc" type="textarea" :rows="4" placeholder="请输入个人描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEdit">确 定</el-button>
        <el-button @click="editOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getProfileByUserId, updateProfile } from "@/api/fuguang/profile";
import { getTimelineByUserId } from "@/api/fuguang/timeline";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "ProfileDetail",
  data() {
    return {
      userId: null,
      userName: '',
      loading: false,
      profile: {
        creditScore: 0,
        taskScore: 0,
        level: 1,
        experience: 0,
        povertyReliefBadge: '0',
        totalTasks: 0,
        completedTasks: 0,
        successRate: 0,
        totalEarnings: 0,
        profileDesc: ''
      },
      timeline: [],
      editOpen: false,
      editForm: {},
      editRules: {
        creditScore: [
          { required: true, message: "信用分不能为空", trigger: "blur" }
        ],
        taskScore: [
          { required: true, message: "任务分不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.userId = this.$route.query.userId;
    this.userName = this.$route.query.userName || '用户';
    console.log('ProfileDetail created, userId:', this.userId, 'userName:', this.userName);
    if (this.userId) {
      this.getProfile();
      this.getTimeline();
    } else {
      this.$modal.msgError("缺少用户ID参数");
    }
  },
  methods: {
    /** 获取个人简介 */
    getProfile() {
      this.loading = true;
      getProfileByUserId(this.userId).then(response => {
        if (response.data) {
          this.profile = { ...this.profile, ...response.data };
        }
        this.loading = false;
      }).catch(error => {
        console.error('获取个人简介失败:', error);
        this.$modal.msgError("获取个人简介失败");
        this.loading = false;
      });
    },
    /** 获取履历时间线 */
    getTimeline() {
      getTimelineByUserId(this.userId).then(response => {
        this.timeline = response.data || [];
      }).catch(error => {
        console.error('获取履历时间线失败:', error);
        this.$modal.msgError("获取履历时间线失败");
      });
    },
    /** 刷新时间线 */
    refreshTimeline() {
      this.getTimeline();
    },
    /** 编辑个人简介 */
    handleEdit() {
      this.editForm = { ...this.profile };
      this.editOpen = true;
    },
    /** 提交编辑 */
    submitEdit() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          updateProfile(this.editForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.editOpen = false;
            this.getProfile();
          });
        }
      });
    },
    /** 返回 */
    goBack() {
      this.$router.go(-1);
    },
    /** 获取信用分类型 */
    getCreditScoreType(score) {
      if (score >= 120) return 'success';
      if (score >= 100) return 'primary';
      if (score >= 80) return 'warning';
      return 'danger';
    },
    /** 获取时间线图标 */
    getTimelineIcon(icon) {
      const iconMap = {
        'user-plus': 'el-icon-user-solid',
        'check-circle': 'el-icon-circle-check',
        'award': 'el-icon-trophy',
        'trophy': 'el-icon-medal'
      };
      return iconMap[icon] || 'el-icon-info';
    },
    /** 时间格式化 */
    parseTime
  }
};
</script>

<style scoped>
.profile-desc {
  min-height: 100px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
}
</style>
