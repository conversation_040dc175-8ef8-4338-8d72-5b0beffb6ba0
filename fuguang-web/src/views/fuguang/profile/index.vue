<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="信用分" prop="creditScore">
        <el-input
          v-model="queryParams.creditScore"
          placeholder="请输入信用分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务分" prop="taskScore">
        <el-input
          v-model="queryParams.taskScore"
          placeholder="请输入任务分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="等级" prop="level">
        <el-input
          v-model="queryParams.level"
          placeholder="请输入等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['fuguang:profile:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['fuguang:profile:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fuguang:profile:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['fuguang:profile:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="profileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="简介ID" align="center" prop="profileId" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="信用分" align="center" prop="creditScore">
        <template slot-scope="scope">
          <el-tag :type="getCreditScoreType(scope.row.creditScore)">{{ scope.row.creditScore }}分</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务分" align="center" prop="taskScore">
        <template slot-scope="scope">
          <el-tag type="primary">{{ scope.row.taskScore }}分</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="等级" align="center" prop="level">
        <template slot-scope="scope">
          <el-tag type="warning">{{ scope.row.level }}级</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="扶贫救援徽章" align="center" prop="povertyReliefBadge">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.povertyReliefBadge === '1'" type="success">已获得</el-tag>
          <el-tag v-else type="info">未获得</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总任务数" align="center" prop="totalTasks" />
      <el-table-column label="完成任务数" align="center" prop="completedTasks" />
      <el-table-column label="成功率" align="center" prop="successRate">
        <template slot-scope="scope">
          {{ scope.row.successRate }}%
        </template>
      </el-table-column>
      <el-table-column label="总收益" align="center" prop="totalEarnings">
        <template slot-scope="scope">
          <el-tag type="danger">¥{{ scope.row.totalEarnings }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['fuguang:profile:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:profile:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:profile:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改个人简介对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="信用分" prop="creditScore">
          <el-input-number v-model="form.creditScore" :min="0" :max="200" />
        </el-form-item>
        <el-form-item label="任务分" prop="taskScore">
          <el-input-number v-model="form.taskScore" :min="0" />
        </el-form-item>
        <el-form-item label="扶贫救援徽章" prop="povertyReliefBadge">
          <el-radio-group v-model="form.povertyReliefBadge">
            <el-radio label="0">未获得</el-radio>
            <el-radio label="1">已获得</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="个人描述" prop="profileDesc">
          <el-input v-model="form.profileDesc" type="textarea" placeholder="请输入个人描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProfile, getProfile, delProfile, addProfile, updateProfile } from "@/api/fuguang/profile";

export default {
  name: "Profile",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 个人简介表格数据
      profileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        creditScore: null,
        taskScore: null,
        level: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        creditScore: [
          { required: true, message: "信用分不能为空", trigger: "blur" }
        ],
        taskScore: [
          { required: true, message: "任务分不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询个人简介列表 */
    getList() {
      this.loading = true;
      listProfile(this.queryParams).then(response => {
        this.profileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        profileId: null,
        userId: null,
        creditScore: 100,
        taskScore: 0,
        povertyReliefBadge: "0",
        profileDesc: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.profileId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加个人简介";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const profileId = row.profileId || this.ids
      getProfile(profileId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改个人简介";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$router.push({
        path: '/fuguang/profile/detail',
        query: { userId: row.userId, userName: '用户' + row.userId }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.profileId != null) {
            updateProfile(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProfile(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const profileIds = row.profileId || this.ids;
      this.$modal.confirm('是否确认删除个人简介编号为"' + profileIds + '"的数据项？').then(function() {
        return delProfile(profileIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/profile/export', {
        ...this.queryParams
      }, `profile_${new Date().getTime()}.xlsx`)
    },
    /** 获取信用分类型 */
    getCreditScoreType(score) {
      if (score >= 120) return 'success';
      if (score >= 100) return 'primary';
      if (score >= 80) return 'warning';
      return 'danger';
    }
  }
};
</script>
