<template>
  <div class="app-container">
    <!-- 系统概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>活跃用户</span>
            <i class="el-icon-user" style="color: #409EFF;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ overview.activeUserCount || 0 }}</div>
            <div class="card-sub">当前活跃用户数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>平台总余额</span>
            <i class="el-icon-wallet" style="color: #67C23A;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.totalBalance || 0 }}</div>
            <div class="card-sub">用户总余额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>累计收入</span>
            <i class="el-icon-plus" style="color: #E6A23C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.totalIncome || 0 }}</div>
            <div class="card-sub">平台累计收入</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>累计提现</span>
            <i class="el-icon-minus" style="color: #F56C6C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ overview.totalWithdraw || 0 }}</div>
            <div class="card-sub">平台累计提现</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 当月数据 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="8">
        <el-card class="overview-card">
          <div class="card-header">
            <span>当月收入</span>
            <i class="el-icon-money" style="color: #67C23A;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #67C23A;">¥{{ overview.currentMonthIncome || 0 }}</div>
            <div class="card-sub">本月总收入</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="overview-card">
          <div class="card-header">
            <span>当月提现</span>
            <i class="el-icon-money" style="color: #F56C6C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #F56C6C;">¥{{ overview.currentMonthWithdraw || 0 }}</div>
            <div class="card-sub">本月总提现</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="overview-card">
          <div class="card-header">
            <span>当月净收入</span>
            <i class="el-icon-pie-chart" style="color: #409EFF;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #409EFF;">¥{{ overview.currentMonthNet || 0 }}</div>
            <div class="card-sub">本月净收入</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 提现统计 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>待审核提现</span>
            <i class="el-icon-time" style="color: #E6A23C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #E6A23C;">{{ overview.pendingWithdrawCount || 0 }}</div>
            <div class="card-sub">¥{{ overview.pendingWithdrawAmount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>成功提现</span>
            <i class="el-icon-success" style="color: #67C23A;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #67C23A;">{{ overview.successWithdrawCount || 0 }}</div>
            <div class="card-sub">成功提现笔数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>失败提现</span>
            <i class="el-icon-error" style="color: #F56C6C;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #F56C6C;">{{ overview.failWithdrawCount || 0 }}</div>
            <div class="card-sub">失败提现笔数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <span>提现成功率</span>
            <i class="el-icon-pie-chart" style="color: #409EFF;"></i>
          </div>
          <div class="card-content">
            <div class="card-value" style="color: #409EFF;">{{ (overview.withdrawSuccessRate || 0).toFixed(1) }}%</div>
            <div class="card-sub">提现成功率</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>收支趋势（最近12个月）</span>
          </div>
          <div id="trendChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>收入类型分布</span>
            <el-date-picker v-model="incomeMonth" type="month" placeholder="选择月份" format="yyyy-MM"
              value-format="yyyy-MM" @change="getIncomeDistribution" style="float: right;" size="small">
            </el-date-picker>
          </div>
          <div id="incomeChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>用户余额分布</span>
          </div>
          <div id="balanceChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>提现渠道分布</span>
          </div>
          <div id="channelChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getSystemOverview, getRevenueTrend, getBalanceDistribution, getIncomeDistribution, getWithdrawChannelDistribution } from "@/api/fuguang/statistics";
import * as echarts from 'echarts';

export default {
  name: "Statistics",
  data() {
    return {
      // 系统概览数据
      overview: {},
      // 收入分布月份选择
      incomeMonth: null,
      // 图表实例
      trendChart: null,
      incomeChart: null,
      balanceChart: null,
      channelChart: null
    };
  },
  created() {
    // 设置默认月份为当前月份
    const now = new Date();
    this.incomeMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

    this.getOverview();
  },
  mounted() {
    this.initCharts();
    this.loadAllData();
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.trendChart) {
      this.trendChart.dispose();
    }
    if (this.incomeChart) {
      this.incomeChart.dispose();
    }
    if (this.balanceChart) {
      this.balanceChart.dispose();
    }
    if (this.channelChart) {
      this.channelChart.dispose();
    }
  },
  methods: {
    /** 获取系统概览 */
    getOverview() {
      getSystemOverview().then(response => {
        this.overview = response.data;
      });
    },
    /** 初始化图表 */
    initCharts() {
      this.trendChart = echarts.init(document.getElementById('trendChart'));
      this.incomeChart = echarts.init(document.getElementById('incomeChart'));
      this.balanceChart = echarts.init(document.getElementById('balanceChart'));
      this.channelChart = echarts.init(document.getElementById('channelChart'));
    },
    /** 加载所有数据 */
    loadAllData() {
      this.getTrendData();
      this.getIncomeDistribution();
      this.getBalanceDistribution();
      this.getChannelDistribution();
    },
    /** 获取趋势数据 */
    getTrendData() {
      getRevenueTrend().then(response => {
        const data = response.data;
        const months = Object.keys(data).sort();
        const incomeData = months.map(month => data[month].income);
        const withdrawData = months.map(month => data[month].withdraw);
        const netData = months.map(month => data[month].net);

        const option = {
          title: {
            text: '收支趋势',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(param => {
                result += param.marker + param.seriesName + ': ¥' + param.value + '<br/>';
              });
              return result;
            }
          },
          legend: {
            data: ['收入', '提现', '净收入'],
            bottom: 0
          },
          xAxis: {
            type: 'category',
            data: months
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '¥{value}'
            }
          },
          series: [
            {
              name: '收入',
              type: 'line',
              data: incomeData,
              itemStyle: { color: '#67C23A' }
            },
            {
              name: '提现',
              type: 'line',
              data: withdrawData,
              itemStyle: { color: '#F56C6C' }
            },
            {
              name: '净收入',
              type: 'line',
              data: netData,
              itemStyle: { color: '#409EFF' }
            }
          ]
        };

        this.trendChart.setOption(option);
      });
    },
    /** 获取收入分布数据 */
    getIncomeDistribution() {
      if (!this.incomeMonth) return;

      const [year, month] = this.incomeMonth.split('-');
      getIncomeDistribution({ year: parseInt(year), month: parseInt(month) }).then(response => {
        const data = response.data;

        const pieData = [
          { value: data.taskCommission, name: '任务佣金' },
          { value: data.recommendReward, name: '推荐奖励' },
          { value: data.otherIncome, name: '其他收入' }
        ];

        const option = {
          title: {
            text: `${year}年${month}月收入分布`,
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: ['任务佣金', '推荐奖励', '其他收入']
          },
          series: [
            {
              name: '收入分布',
              type: 'pie',
              radius: '50%',
              data: pieData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };

        this.incomeChart.setOption(option);
      });
    },
    /** 获取余额分布数据 */
    getBalanceDistribution() {
      getBalanceDistribution().then(response => {
        const data = response.data;
        const categories = Object.keys(data);
        const values = Object.values(data);

        const option = {
          title: {
            text: '用户余额分布',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: categories,
            axisLabel: {
              formatter: '¥{value}'
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}人'
            }
          },
          series: [
            {
              name: '用户数量',
              type: 'bar',
              data: values,
              itemStyle: {
                color: '#409EFF'
              }
            }
          ]
        };

        this.balanceChart.setOption(option);
      });
    },
    /** 获取渠道分布数据 */
    getChannelDistribution() {
      getWithdrawChannelDistribution().then(response => {
        const data = response.data;
        const channels = Object.keys(data);
        const chartData = channels.map(channel => ({
          name: channel,
          value: data[channel].count
        }));

        const option = {
          title: {
            text: '提现渠道分布',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: function (params) {
              const channelData = data[params.name];
              return `${params.name}<br/>
                      提现次数: ${channelData.count}<br/>
                      提现金额: ¥${channelData.amount}<br/>
                      成功率: ${channelData.successRate.toFixed(1)}%`;
            }
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: channels
          },
          series: [
            {
              name: '提现渠道',
              type: 'pie',
              radius: '50%',
              data: chartData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };

        this.channelChart.setOption(option);
      });
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #909399;
}

.card-header i {
  font-size: 20px;
}

.card-content {
  margin-top: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-sub {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
