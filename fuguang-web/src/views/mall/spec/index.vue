<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品ID" prop="productId">
        <el-input
          v-model="queryParams.productId"
          placeholder="请输入商品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规格名称" prop="specName">
        <el-input
          v-model="queryParams.specName"
          placeholder="请输入规格名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规格状态" prop="specStatus">
        <el-select v-model="queryParams.specStatus" placeholder="请选择规格状态" clearable>
          <el-option
            v-for="dict in dict.type.mall_spec_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mall:spec:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mall:spec:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mall:spec:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:spec:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="specList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="规格ID" align="center" prop="specId" />
      <el-table-column label="商品ID" align="center" prop="productId" />
      <el-table-column label="规格名称" align="center" prop="specName" />
      <el-table-column label="规格图片" align="center" prop="specImage" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.specImage" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="售卖价格" align="center" prop="salePrice" />
      <el-table-column label="供货价格" align="center" prop="supplyPrice" />
      <el-table-column label="库存数量" align="center" prop="stockQuantity" />
      <el-table-column label="规格状态" align="center" prop="specStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.mall_spec_status" :value="scope.row.specStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="显示顺序" align="center" prop="sortOrder" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mall:spec:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mall:spec:remove']"
          >删除</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['mall:spec:edit']">
            <span class="el-dropdown-link">
              <i class="el-icon-d-arrow-right el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handleOnShelf" icon="el-icon-top" v-if="scope.row.specStatus == '1'">上架</el-dropdown-item>
              <el-dropdown-item command="handleOffShelf" icon="el-icon-bottom" v-if="scope.row.specStatus == '0'">下架</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改商品规格对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品ID" prop="productId">
          <el-input v-model="form.productId" placeholder="请输入商品ID" />
        </el-form-item>
        <el-form-item label="规格名称" prop="specName">
          <el-input v-model="form.specName" placeholder="请输入规格名称" />
        </el-form-item>
        <el-form-item label="规格图片" prop="specImage">
          <image-upload v-model="form.specImage"/>
        </el-form-item>
        <el-form-item label="售卖价格" prop="salePrice">
          <el-input v-model="form.salePrice" placeholder="请输入售卖价格" />
        </el-form-item>
        <el-form-item label="供货价格" prop="supplyPrice">
          <el-input v-model="form.supplyPrice" placeholder="请输入供货价格" />
        </el-form-item>
        <el-form-item label="库存数量" prop="stockQuantity">
          <el-input v-model="form.stockQuantity" placeholder="请输入库存数量" />
        </el-form-item>
        <el-form-item label="规格状态" prop="specStatus">
          <el-radio-group v-model="form.specStatus">
            <el-radio
              v-for="dict in dict.type.mall_spec_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="显示顺序" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入显示顺序" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSpec, getSpec, delSpec, addSpec, updateSpec, onShelfSpec, offShelfSpec } from "@/api/mall/spec";

export default {
  name: "Spec",
  dicts: ['mall_spec_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品规格表格数据
      specList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productId: null,
        specName: null,
        specStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productId: [
          { required: true, message: "商品ID不能为空", trigger: "blur" }
        ],
        specName: [
          { required: true, message: "规格名称不能为空", trigger: "blur" }
        ],
        salePrice: [
          { required: true, message: "售卖价格不能为空", trigger: "blur" }
        ],
        stockQuantity: [
          { required: true, message: "库存数量不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商品规格列表 */
    getList() {
      this.loading = true;
      listSpec(this.queryParams).then(response => {
        this.specList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        specId: null,
        productId: null,
        specName: null,
        specImage: null,
        salePrice: null,
        supplyPrice: null,
        stockQuantity: null,
        specStatus: "0",
        sortOrder: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.specId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商品规格";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const specId = row.specId || this.ids
      getSpec(specId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商品规格";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.specId != null) {
            updateSpec(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSpec(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const specIds = row.specId || this.ids;
      this.$modal.confirm('是否确认删除商品规格编号为"' + specIds + '"的数据项？').then(function() {
        return delSpec(specIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mall/spec/export', {
        ...this.queryParams
      }, `spec_${new Date().getTime()}.xlsx`)
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "handleOnShelf":
          this.handleOnShelf(row);
          break;
        case "handleOffShelf":
          this.handleOffShelf(row);
          break;
        default:
          break;
      }
    },
    /** 上架按钮操作 */
    handleOnShelf(row) {
      const specIds = row.specId || this.ids;
      this.$modal.confirm('是否确认上架规格编号为"' + specIds + '"的数据项？').then(function() {
        return onShelfSpec(specIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("上架成功");
      }).catch(() => {});
    },
    /** 下架按钮操作 */
    handleOffShelf(row) {
      const specIds = row.specId || this.ids;
      this.$modal.confirm('是否确认下架规格编号为"' + specIds + '"的数据项？').then(function() {
        return offShelfSpec(specIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("下架成功");
      }).catch(() => {});
    }
  }
};
</script>
