package com.ruoyi.web.controller.fuguang;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.WithdrawRecord;
import com.ruoyi.fuguang.service.IWithdrawService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 提现管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController("withdrawManageController")
@RequestMapping("/fuguang/withdraw")
public class WithdrawManageController extends BaseController
{
    @Autowired
    private IWithdrawService withdrawService;

    /**
     * 查询提现记录列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:list')")
    @GetMapping("/list")
    public TableDataInfo list(WithdrawRecord withdrawRecord)
    {
        startPage();
        List<WithdrawRecord> list = withdrawService.selectWithdrawRecordList(withdrawRecord);
        return getDataTable(list);
    }

    /**
     * 导出提现记录列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:export')")
    @Log(title = "提现记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WithdrawRecord withdrawRecord)
    {
        List<WithdrawRecord> list = withdrawService.selectWithdrawRecordList(withdrawRecord);
        ExcelUtil<WithdrawRecord> util = new ExcelUtil<WithdrawRecord>(WithdrawRecord.class);
        util.exportExcel(response, list, "提现记录数据");
    }

    /**
     * 获取提现记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:query')")
    @GetMapping(value = "/{withdrawId}")
    public AjaxResult getInfo(@PathVariable("withdrawId") Long withdrawId)
    {
        WithdrawRecord record = withdrawService.selectWithdrawRecordByWithdrawId(withdrawId);
        if (record == null) {
            return error("提现记录不存在");
        }
        
        // 构建详细信息
        Map<String, Object> result = new HashMap<>();
        result.put("withdrawId", record.getWithdrawId());
        result.put("userId", record.getUserId());
        result.put("userName", record.getUserName());
        result.put("withdrawNo", record.getWithdrawNo());
        result.put("withdrawAmount", record.getWithdrawAmount());
        result.put("withdrawFee", record.getWithdrawFee());
        result.put("actualAmount", record.getActualAmount());
        result.put("withdrawType", record.getWithdrawType());
        result.put("withdrawTypeName", record.getWithdrawTypeName());
        result.put("withdrawStatus", record.getWithdrawStatus());
        result.put("withdrawStatusName", record.getWithdrawStatusName());
        result.put("payeeAccount", record.getPayeeAccount());
        result.put("payeeName", record.getPayeeName());
        result.put("tradeNo", record.getTradeNo());
        result.put("failReason", record.getFailReason());
        result.put("applyTime", record.getApplyTime());
        result.put("processTime", record.getProcessTime());
        result.put("finishTime", record.getFinishTime());
        result.put("createTime", record.getCreateTime());
        result.put("updateTime", record.getUpdateTime());
        
        return success(result);
    }

    /**
     * 根据用户ID查询提现记录
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:query')")
    @GetMapping(value = "/user/{userId}")
    public AjaxResult getRecordsByUserId(@PathVariable("userId") Long userId)
    {
        List<WithdrawRecord> records = withdrawService.selectWithdrawRecordsByUserId(userId);
        return success(records);
    }

    /**
     * 审核提现申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:audit')")
    @Log(title = "审核提现申请", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult auditWithdraw(@RequestParam Long withdrawId, 
                                   @RequestParam String action,
                                   @RequestParam(required = false) String reason)
    {
        WithdrawRecord record = withdrawService.selectWithdrawRecordByWithdrawId(withdrawId);
        if (record == null) {
            return error("提现记录不存在");
        }

        if (!"0".equals(record.getWithdrawStatus())) {
            return error("只能审核申请中的提现记录");
        }

        Map<String, Object> result;
        if ("approve".equals(action)) {
            // 审核通过，处理提现
            result = withdrawService.processWithdraw(withdrawId,true, reason);
        } else if ("reject".equals(action)) {
            // 审核拒绝，返回佣金
            result = withdrawService.rejectWithdraw(record.getWithdrawNo(), reason);
        } else {
            return error("审核操作类型错误");
        }

        if ((Boolean) result.get("success")) {
            return success(result.get("message"));
        } else {
            return error((String) result.get("message"));
        }
    }

    /**
     * 批量审核提现申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:audit')")
    @Log(title = "批量审核提现申请", businessType = BusinessType.UPDATE)
    @PostMapping("/batchAudit")
    public AjaxResult batchAuditWithdraw(@RequestParam Long[] withdrawIds, 
                                        @RequestParam String action,
                                        @RequestParam(required = false) String reason)
    {
        int successCount = 0;
        int failCount = 0;
        StringBuilder failMessages = new StringBuilder();

        for (Long withdrawId : withdrawIds) {
            try {
                WithdrawRecord record = withdrawService.selectWithdrawRecordByWithdrawId(withdrawId);
                if (record == null || !"0".equals(record.getWithdrawStatus())) {
                    failCount++;
                    failMessages.append("提现记录").append(withdrawId).append("状态异常；");
                    continue;
                }

                Map<String, Object> result;
                if ("approve".equals(action)) {
                    result = withdrawService.processWithdraw(record.getWithdrawId(), true, reason);
                } else if ("reject".equals(action)) {
                    result = withdrawService.rejectWithdraw(record.getWithdrawNo(), reason);
                } else {
                    failCount++;
                    failMessages.append("操作类型错误；");
                    continue;
                }

                if ((Boolean) result.get("success")) {
                    successCount++;
                } else {
                    failCount++;
                    failMessages.append(result.get("message")).append("；");
                }
            } catch (Exception e) {
                failCount++;
                failMessages.append("处理提现记录").append(withdrawId).append("时发生异常；");
            }
        }

        String message = String.format("批量审核完成，成功：%d条，失败：%d条", successCount, failCount);
        if (failCount > 0) {
            message += "，失败原因：" + failMessages.toString();
        }

        return success(message);
    }

    /**
     * 获取待审核提现数量
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:query')")
    @GetMapping("/pendingCount")
    public AjaxResult getPendingCount()
    {
        WithdrawRecord query = new WithdrawRecord();
        query.setWithdrawStatus("0"); // 申请中
        List<WithdrawRecord> pendingRecords = withdrawService.selectWithdrawRecordList(query);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", pendingRecords.size());
        
        // 计算待审核金额
        BigDecimal pendingAmount = BigDecimal.ZERO;
        for (WithdrawRecord record : pendingRecords) {
            pendingAmount = pendingAmount.add(record.getWithdrawAmount());
        }
        result.put("amount", pendingAmount);
        
        return success(result);
    }

    /**
     * 获取提现统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:withdraw:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getWithdrawStatistics(@RequestParam(required = false) String startDate,
                                           @RequestParam(required = false) String endDate)
    {
        List<WithdrawRecord> allRecords = withdrawService.selectWithdrawRecordList(new WithdrawRecord());
        
        Map<String, Object> statistics = new HashMap<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal successAmount = BigDecimal.ZERO;
        BigDecimal failAmount = BigDecimal.ZERO;
        BigDecimal pendingAmount = BigDecimal.ZERO;
        BigDecimal totalFee = BigDecimal.ZERO;
        
        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;
        int pendingCount = 0;
        
        // 按渠道统计
        Map<String, Integer> channelCount = new HashMap<>();
        Map<String, BigDecimal> channelAmount = new HashMap<>();
        
        for (WithdrawRecord record : allRecords) {
            totalCount++;
            totalAmount = totalAmount.add(record.getWithdrawAmount());
            totalFee = totalFee.add(record.getWithdrawFee());
            
            // 按状态统计
            switch (record.getWithdrawStatus()) {
                case "2": // 提现成功
                    successCount++;
                    successAmount = successAmount.add(record.getWithdrawAmount());
                    break;
                case "3": // 提现失败
                    failCount++;
                    failAmount = failAmount.add(record.getWithdrawAmount());
                    break;
                case "0": // 申请中
                case "1": // 处理中
                    pendingCount++;
                    pendingAmount = pendingAmount.add(record.getWithdrawAmount());
                    break;
            }
            
            // 按渠道统计
            String channelName = record.getWithdrawTypeName();
            channelCount.put(channelName, channelCount.getOrDefault(channelName, 0) + 1);
            channelAmount.put(channelName, channelAmount.getOrDefault(channelName, BigDecimal.ZERO).add(record.getWithdrawAmount()));
        }
        
        statistics.put("totalCount", totalCount);
        statistics.put("totalAmount", totalAmount);
        statistics.put("totalFee", totalFee);
        statistics.put("successCount", successCount);
        statistics.put("successAmount", successAmount);
        statistics.put("failCount", failCount);
        statistics.put("failAmount", failAmount);
        statistics.put("pendingCount", pendingCount);
        statistics.put("pendingAmount", pendingAmount);
        statistics.put("successRate", totalCount > 0 ? successCount * 100.0 / totalCount : 0);
        statistics.put("channelCount", channelCount);
        statistics.put("channelAmount", channelAmount);
        
        return success(statistics);
    }
}
