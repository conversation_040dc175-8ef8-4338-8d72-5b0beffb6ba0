package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppUserTimeline;
import com.ruoyi.fuguang.service.IAppUserTimelineService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户履历时间线Controller
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@RestController
@RequestMapping("/fuguang/timeline")
public class AppUserTimelineController extends BaseController
{
    @Autowired
    private IAppUserTimelineService appUserTimelineService;

    /**
     * 查询APP用户履历时间线列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:timeline:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserTimeline appUserTimeline)
    {
        startPage();
        List<AppUserTimeline> list = appUserTimelineService.selectAppUserTimelineList(appUserTimeline);
        return getDataTable(list);
    }

    /**
     * 根据用户ID查询履历时间线
     */
    @PreAuthorize("@ss.hasPermi('fuguang:timeline:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getTimelineByUserId(@PathVariable Long userId)
    {
        List<AppUserTimeline> list = appUserTimelineService.selectAppUserTimelineByUserId(userId);
        return success(list);
    }

    /**
     * 导出APP用户履历时间线列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:timeline:export')")
    @Log(title = "APP用户履历时间线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserTimeline appUserTimeline)
    {
        List<AppUserTimeline> list = appUserTimelineService.selectAppUserTimelineList(appUserTimeline);
        ExcelUtil<AppUserTimeline> util = new ExcelUtil<AppUserTimeline>(AppUserTimeline.class);
        util.exportExcel(response, list, "APP用户履历时间线数据");
    }

    /**
     * 获取APP用户履历时间线详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:timeline:query')")
    @GetMapping(value = "/{timelineId}")
    public AjaxResult getInfo(@PathVariable("timelineId") Long timelineId)
    {
        return success(appUserTimelineService.selectAppUserTimelineByTimelineId(timelineId));
    }

    /**
     * 新增APP用户履历时间线
     */
    @PreAuthorize("@ss.hasPermi('fuguang:timeline:add')")
    @Log(title = "APP用户履历时间线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserTimeline appUserTimeline)
    {
        return toAjax(appUserTimelineService.insertAppUserTimeline(appUserTimeline));
    }

    /**
     * 修改APP用户履历时间线
     */
    @PreAuthorize("@ss.hasPermi('fuguang:timeline:edit')")
    @Log(title = "APP用户履历时间线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserTimeline appUserTimeline)
    {
        return toAjax(appUserTimelineService.updateAppUserTimeline(appUserTimeline));
    }

    /**
     * 删除APP用户履历时间线
     */
    @PreAuthorize("@ss.hasPermi('fuguang:timeline:remove')")
    @Log(title = "APP用户履历时间线", businessType = BusinessType.DELETE)
	@DeleteMapping("/{timelineIds}")
    public AjaxResult remove(@PathVariable Long[] timelineIds)
    {
        return toAjax(appUserTimelineService.deleteAppUserTimelineByTimelineIds(timelineIds));
    }
}
