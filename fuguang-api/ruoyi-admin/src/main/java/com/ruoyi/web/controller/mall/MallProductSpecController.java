package com.ruoyi.web.controller.mall;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.fuguang.domain.MallProductSpec;
import com.ruoyi.fuguang.service.IMallProductSpecService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品规格Controller
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/mall/spec")
public class MallProductSpecController extends BaseController
{
    @Autowired
    private IMallProductSpecService mallProductSpecService;

    /**
     * 查询商品规格列表
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:list')")
    @GetMapping("/list")
    public TableDataInfo list(MallProductSpec mallProductSpec)
    {
        startPage();
        List<MallProductSpec> list = mallProductSpecService.selectMallProductSpecList(mallProductSpec);
        return getDataTable(list);
    }

    /**
     * 根据商品ID查询规格列表
     */
    @GetMapping("/listByProduct/{productId}")
    public AjaxResult listByProduct(@PathVariable("productId") Long productId)
    {
        List<MallProductSpec> list = mallProductSpecService.selectMallProductSpecByProductId(productId);
        return success(list);
    }

    /**
     * 导出商品规格列表
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:export')")
    @Log(title = "商品规格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MallProductSpec mallProductSpec)
    {
        List<MallProductSpec> list = mallProductSpecService.selectMallProductSpecList(mallProductSpec);
        ExcelUtil<MallProductSpec> util = new ExcelUtil<MallProductSpec>(MallProductSpec.class);
        util.exportExcel(response, list, "商品规格数据");
    }

    /**
     * 获取商品规格详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:query')")
    @GetMapping(value = "/{specId}")
    public AjaxResult getInfo(@PathVariable("specId") Long specId)
    {
        return success(mallProductSpecService.selectMallProductSpecBySpecId(specId));
    }

    /**
     * 新增商品规格
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:add')")
    @Log(title = "商品规格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MallProductSpec mallProductSpec)
    {
        return toAjax(mallProductSpecService.insertMallProductSpec(mallProductSpec));
    }

    /**
     * 修改商品规格
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:edit')")
    @Log(title = "商品规格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MallProductSpec mallProductSpec)
    {
        return toAjax(mallProductSpecService.updateMallProductSpec(mallProductSpec));
    }

    /**
     * 删除商品规格
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:remove')")
    @Log(title = "商品规格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{specIds}")
    public AjaxResult remove(@PathVariable Long[] specIds)
    {
        return toAjax(mallProductSpecService.deleteMallProductSpecBySpecIds(specIds));
    }

    /**
     * 上架规格
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:edit')")
    @Log(title = "上架规格", businessType = BusinessType.UPDATE)
    @PutMapping("/onShelf/{specId}")
    public AjaxResult onShelf(@PathVariable Long specId)
    {
        MallProductSpec spec = new MallProductSpec();
        spec.setSpecId(specId);
        spec.setSpecStatus("0");
        return toAjax(mallProductSpecService.updateMallProductSpec(spec));
    }

    /**
     * 下架规格
     */
    @PreAuthorize("@ss.hasPermi('mall:spec:edit')")
    @Log(title = "下架规格", businessType = BusinessType.UPDATE)
    @PutMapping("/offShelf/{specId}")
    public AjaxResult offShelf(@PathVariable Long specId)
    {
        MallProductSpec spec = new MallProductSpec();
        spec.setSpecId(specId);
        spec.setSpecStatus("1");
        return toAjax(mallProductSpecService.updateMallProductSpec(spec));
    }
}
