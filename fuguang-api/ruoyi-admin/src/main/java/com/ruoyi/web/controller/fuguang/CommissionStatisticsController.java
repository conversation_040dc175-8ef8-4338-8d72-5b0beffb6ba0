package com.ruoyi.web.controller.fuguang;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.CommissionBill;
import com.ruoyi.fuguang.domain.UserBalance;
import com.ruoyi.fuguang.domain.WithdrawRecord;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.IWithdrawService;
import com.ruoyi.fuguang.service.IBalanceRecordService;

/**
 * 佣金系统统计Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController("commissionStatisticsController")
@RequestMapping("/fuguang/statistics")
public class CommissionStatisticsController extends BaseController
{
    @Autowired
    private ICommissionBillService commissionBillService;

    @Autowired
    private IUserBalanceService userBalanceService;

    @Autowired
    private IWithdrawService withdrawService;

    @Autowired
    private IBalanceRecordService balanceRecordService;

    /**
     * 获取平台佣金系统总览
     */
    @PreAuthorize("@ss.hasPermi('fuguang:statistics:overview')")
    @GetMapping("/overview")
    public AjaxResult getSystemOverview()
    {
        Map<String, Object> overview = new HashMap<>();
        
        // 用户余额统计
        List<UserBalance> allBalances = userBalanceService.selectUserBalanceList(new UserBalance());
        BigDecimal totalBalance = BigDecimal.ZERO;
        BigDecimal totalIncome = BigDecimal.ZERO;
        BigDecimal totalWithdraw = BigDecimal.ZERO;
        int activeUserCount = allBalances.size();
        
        for (UserBalance balance : allBalances) {
            totalBalance = totalBalance.add(balance.getTotalBalance());
            totalIncome = totalIncome.add(balance.getTotalIncome());
            totalWithdraw = totalWithdraw.add(balance.getTotalWithdraw());
        }
        
        // 提现统计
        List<WithdrawRecord> allWithdraws = withdrawService.selectWithdrawRecordList(new WithdrawRecord());
        int totalWithdrawCount = allWithdraws.size();
        int pendingWithdrawCount = 0;
        int successWithdrawCount = 0;
        int failWithdrawCount = 0;
        BigDecimal pendingWithdrawAmount = BigDecimal.ZERO;
        
        for (WithdrawRecord record : allWithdraws) {
            switch (record.getWithdrawStatus()) {
                case "0": // 申请中
                case "1": // 处理中
                    pendingWithdrawCount++;
                    pendingWithdrawAmount = pendingWithdrawAmount.add(record.getWithdrawAmount());
                    break;
                case "2": // 提现成功
                    successWithdrawCount++;
                    break;
                case "3": // 提现失败
                    failWithdrawCount++;
                    break;
            }
        }
        
        // 当月佣金统计
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        int currentMonth = calendar.get(Calendar.MONTH) + 1;
        
        CommissionBill queryBill = new CommissionBill();
        queryBill.setBillYear(currentYear);
        queryBill.setBillMonth(currentMonth);
        List<CommissionBill> currentMonthBills = commissionBillService.selectCommissionBillList(queryBill);
        
        BigDecimal currentMonthIncome = BigDecimal.ZERO;
        BigDecimal currentMonthWithdraw = BigDecimal.ZERO;
        
        for (CommissionBill bill : currentMonthBills) {
            currentMonthIncome = currentMonthIncome.add(bill.getTotalIncome());
            currentMonthWithdraw = currentMonthWithdraw.add(bill.getTotalWithdraw());
        }
        
        overview.put("activeUserCount", activeUserCount);
        overview.put("totalBalance", totalBalance);
        overview.put("totalIncome", totalIncome);
        overview.put("totalWithdraw", totalWithdraw);
        overview.put("netIncome", totalIncome.subtract(totalWithdraw));
        overview.put("totalWithdrawCount", totalWithdrawCount);
        overview.put("pendingWithdrawCount", pendingWithdrawCount);
        overview.put("pendingWithdrawAmount", pendingWithdrawAmount);
        overview.put("successWithdrawCount", successWithdrawCount);
        overview.put("failWithdrawCount", failWithdrawCount);
        overview.put("withdrawSuccessRate", totalWithdrawCount > 0 ? successWithdrawCount * 100.0 / totalWithdrawCount : 0);
        overview.put("currentMonthIncome", currentMonthIncome);
        overview.put("currentMonthWithdraw", currentMonthWithdraw);
        overview.put("currentMonthNet", currentMonthIncome.subtract(currentMonthWithdraw));
        
        return success(overview);
    }

    /**
     * 获取平台收支趋势（最近12个月）
     */
    @PreAuthorize("@ss.hasPermi('fuguang:statistics:trend')")
    @GetMapping("/trend")
    public AjaxResult getRevenueTrend()
    {
        Calendar calendar = Calendar.getInstance();
        Map<String, Map<String, Object>> trendData = new HashMap<>();
        
        // 获取最近12个月的数据
        for (int i = 11; i >= 0; i--) {
            Calendar tempCalendar = (Calendar) calendar.clone();
            tempCalendar.add(Calendar.MONTH, -i);
            int year = tempCalendar.get(Calendar.YEAR);
            int month = tempCalendar.get(Calendar.MONTH) + 1;
            
            CommissionBill queryBill = new CommissionBill();
            queryBill.setBillYear(year);
            queryBill.setBillMonth(month);
            List<CommissionBill> bills = commissionBillService.selectCommissionBillList(queryBill);
            
            BigDecimal monthlyIncome = BigDecimal.ZERO;
            BigDecimal monthlyWithdraw = BigDecimal.ZERO;
            BigDecimal taskCommission = BigDecimal.ZERO;
            BigDecimal recommendReward = BigDecimal.ZERO;
            BigDecimal otherIncome = BigDecimal.ZERO;
            
            for (CommissionBill bill : bills) {
                monthlyIncome = monthlyIncome.add(bill.getTotalIncome());
                monthlyWithdraw = monthlyWithdraw.add(bill.getTotalWithdraw());
                taskCommission = taskCommission.add(bill.getTaskCommission());
                recommendReward = recommendReward.add(bill.getRecommendReward());
                otherIncome = otherIncome.add(bill.getOtherIncome());
            }
            
            String monthKey = String.format("%d-%02d", year, month);
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("income", monthlyIncome);
            monthData.put("withdraw", monthlyWithdraw);
            monthData.put("net", monthlyIncome.subtract(monthlyWithdraw));
            monthData.put("userCount", bills.size());
            monthData.put("taskCommission", taskCommission);
            monthData.put("recommendReward", recommendReward);
            monthData.put("otherIncome", otherIncome);
            
            trendData.put(monthKey, monthData);
        }
        
        return success(trendData);
    }

    /**
     * 获取用户余额分布统计
     */
    @PreAuthorize("@ss.hasPermi('fuguang:statistics:balance-distribution')")
    @GetMapping("/balance-distribution")
    public AjaxResult getBalanceDistribution()
    {
        List<UserBalance> allBalances = userBalanceService.selectUserBalanceList(new UserBalance());
        
        Map<String, Integer> distribution = new HashMap<>();
        distribution.put("0-10", 0);
        distribution.put("10-50", 0);
        distribution.put("50-100", 0);
        distribution.put("100-500", 0);
        distribution.put("500-1000", 0);
        distribution.put("1000+", 0);
        
        for (UserBalance balance : allBalances) {
            BigDecimal totalBalance = balance.getTotalBalance();
            if (totalBalance.compareTo(new BigDecimal("10")) < 0) {
                distribution.put("0-10", distribution.get("0-10") + 1);
            } else if (totalBalance.compareTo(new BigDecimal("50")) < 0) {
                distribution.put("10-50", distribution.get("10-50") + 1);
            } else if (totalBalance.compareTo(new BigDecimal("100")) < 0) {
                distribution.put("50-100", distribution.get("50-100") + 1);
            } else if (totalBalance.compareTo(new BigDecimal("500")) < 0) {
                distribution.put("100-500", distribution.get("100-500") + 1);
            } else if (totalBalance.compareTo(new BigDecimal("1000")) < 0) {
                distribution.put("500-1000", distribution.get("500-1000") + 1);
            } else {
                distribution.put("1000+", distribution.get("1000+") + 1);
            }
        }
        
        return success(distribution);
    }

    /**
     * 获取收入类型分布统计
     */
    @PreAuthorize("@ss.hasPermi('fuguang:statistics:income-distribution')")
    @GetMapping("/income-distribution")
    public AjaxResult getIncomeDistribution(@RequestParam(required = false) Integer year,
                                           @RequestParam(required = false) Integer month)
    {
        // 如果没有指定年月，使用当前年月
        if (year == null || month == null) {
            Calendar calendar = Calendar.getInstance();
            year = calendar.get(Calendar.YEAR);
            month = calendar.get(Calendar.MONTH) + 1;
        }

        CommissionBill queryBill = new CommissionBill();
        queryBill.setBillYear(year);
        queryBill.setBillMonth(month);
        List<CommissionBill> bills = commissionBillService.selectCommissionBillList(queryBill);

        BigDecimal taskCommissionTotal = BigDecimal.ZERO;
        BigDecimal recommendRewardTotal = BigDecimal.ZERO;
        BigDecimal otherIncomeTotal = BigDecimal.ZERO;

        for (CommissionBill bill : bills) {
            taskCommissionTotal = taskCommissionTotal.add(bill.getTaskCommission());
            recommendRewardTotal = recommendRewardTotal.add(bill.getRecommendReward());
            otherIncomeTotal = otherIncomeTotal.add(bill.getOtherIncome());
        }

        BigDecimal total = taskCommissionTotal.add(recommendRewardTotal).add(otherIncomeTotal);

        Map<String, Object> distribution = new HashMap<>();
        distribution.put("year", year);
        distribution.put("month", month);
        distribution.put("taskCommission", taskCommissionTotal);
        distribution.put("recommendReward", recommendRewardTotal);
        distribution.put("otherIncome", otherIncomeTotal);
        distribution.put("total", total);
        
        // 计算百分比
        if (total.compareTo(BigDecimal.ZERO) > 0) {
            distribution.put("taskCommissionPercent", taskCommissionTotal.divide(total, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
            distribution.put("recommendRewardPercent", recommendRewardTotal.divide(total, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
            distribution.put("otherIncomePercent", otherIncomeTotal.divide(total, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
        } else {
            distribution.put("taskCommissionPercent", BigDecimal.ZERO);
            distribution.put("recommendRewardPercent", BigDecimal.ZERO);
            distribution.put("otherIncomePercent", BigDecimal.ZERO);
        }

        return success(distribution);
    }

    /**
     * 获取提现渠道分布统计
     */
    @PreAuthorize("@ss.hasPermi('fuguang:statistics:withdraw-channel')")
    @GetMapping("/withdraw-channel")
    public AjaxResult getWithdrawChannelDistribution()
    {
        List<WithdrawRecord> allWithdraws = withdrawService.selectWithdrawRecordList(new WithdrawRecord());
        
        Map<String, Map<String, Object>> channelStats = new HashMap<>();
        
        for (WithdrawRecord record : allWithdraws) {
            String channelName = record.getWithdrawTypeName();
            
            if (!channelStats.containsKey(channelName)) {
                channelStats.put(channelName, new HashMap<>());
                channelStats.get(channelName).put("count", 0);
                channelStats.get(channelName).put("amount", BigDecimal.ZERO);
                channelStats.get(channelName).put("successCount", 0);
                channelStats.get(channelName).put("successAmount", BigDecimal.ZERO);
            }
            
            Map<String, Object> channelData = channelStats.get(channelName);
            channelData.put("count", (Integer) channelData.get("count") + 1);
            channelData.put("amount", ((BigDecimal) channelData.get("amount")).add(record.getWithdrawAmount()));
            
            if ("2".equals(record.getWithdrawStatus())) { // 提现成功
                channelData.put("successCount", (Integer) channelData.get("successCount") + 1);
                channelData.put("successAmount", ((BigDecimal) channelData.get("successAmount")).add(record.getWithdrawAmount()));
            }
        }
        
        // 计算成功率
        for (Map<String, Object> channelData : channelStats.values()) {
            int totalCount = (Integer) channelData.get("count");
            int successCount = (Integer) channelData.get("successCount");
            double successRate = totalCount > 0 ? successCount * 100.0 / totalCount : 0;
            channelData.put("successRate", successRate);
        }
        
        return success(channelStats);
    }
}
