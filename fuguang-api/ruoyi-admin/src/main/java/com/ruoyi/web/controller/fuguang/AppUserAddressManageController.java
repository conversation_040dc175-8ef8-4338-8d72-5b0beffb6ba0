package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppUserAddress;
import com.ruoyi.fuguang.service.IAppUserAddressService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;

/**
 * APP用户地址管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@RestController("appUserAddressManageController")
@RequestMapping("/fuguang/address")
public class AppUserAddressManageController extends BaseController
{
    @Autowired
    private IAppUserAddressService appUserAddressService;

    /**
     * 查询APP用户地址列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserAddress appUserAddress)
    {
        startPage();
        List<AppUserAddress> list = appUserAddressService.selectAppUserAddressList(appUserAddress);
        return getDataTable(list);
    }

    /**
     * 导出APP用户地址列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:export')")
    @Log(title = "APP用户地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserAddress appUserAddress)
    {
        List<AppUserAddress> list = appUserAddressService.selectAppUserAddressList(appUserAddress);
        ExcelUtil<AppUserAddress> util = new ExcelUtil<AppUserAddress>(AppUserAddress.class);
        util.exportExcel(response, list, "APP用户地址数据");
    }

    /**
     * 获取APP用户地址详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:query')")
    @GetMapping(value = "/{addressId}")
    public AjaxResult getInfo(@PathVariable("addressId") Long addressId)
    {
        return success(appUserAddressService.selectAppUserAddressByAddressId(addressId));
    }

    /**
     * 新增APP用户地址
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:add')")
    @Log(title = "APP用户地址", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserAddress appUserAddress)
    {
        // 数据校验
        String validateResult = appUserAddressService.validateAddressData(appUserAddress);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        // 检查联系人手机号是否唯一（同一用户下）
        if (!appUserAddressService.checkContactPhoneUnique(appUserAddress))
        {
            return error("新增地址失败，该用户下联系人手机号已存在");
        }
        
        appUserAddress.setCreateBy(getUsername());
        return toAjax(appUserAddressService.insertAppUserAddress(appUserAddress));
    }

    /**
     * 修改APP用户地址
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:edit')")
    @Log(title = "APP用户地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserAddress appUserAddress)
    {
        // 数据校验
        String validateResult = appUserAddressService.validateAddressData(appUserAddress);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        // 检查联系人手机号是否唯一（同一用户下）
        if (!appUserAddressService.checkContactPhoneUnique(appUserAddress))
        {
            return error("修改地址失败，该用户下联系人手机号已存在");
        }
        
        appUserAddress.setUpdateBy(getUsername());
        return toAjax(appUserAddressService.updateAppUserAddress(appUserAddress));
    }

    /**
     * 删除APP用户地址
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:remove')")
    @Log(title = "APP用户地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{addressIds}")
    public AjaxResult remove(@PathVariable Long[] addressIds)
    {
        return toAjax(appUserAddressService.deleteAppUserAddressByAddressIds(addressIds));
    }

    /**
     * 设置默认地址
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:edit')")
    @Log(title = "APP用户地址", businessType = BusinessType.UPDATE)
    @PutMapping("/setDefault")
    public AjaxResult setDefault(@RequestBody AppUserAddress appUserAddress)
    {
        if (appUserAddress.getAddressId() == null || appUserAddress.getUserId() == null)
        {
            return error("参数错误");
        }
        
        int result = appUserAddressService.setDefaultAddress(appUserAddress.getAddressId(), appUserAddress.getUserId());
        return toAjax(result);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:edit')")
    @Log(title = "APP用户地址", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody AppUserAddress appUserAddress)
    {
        appUserAddress.setUpdateBy(getUsername());
        return toAjax(appUserAddressService.updateAppUserAddress(appUserAddress));
    }

    /**
     * 根据用户ID查询地址列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getAddressByUserId(@PathVariable("userId") Long userId)
    {
        List<AppUserAddress> list = appUserAddressService.selectAppUserAddressListByUserId(userId);
        return success(list);
    }

    /**
     * 查询用户默认地址
     */
    @PreAuthorize("@ss.hasPermi('fuguang:address:query')")
    @GetMapping("/default/{userId}")
    public AjaxResult getDefaultAddress(@PathVariable("userId") Long userId)
    {
        AppUserAddress address = appUserAddressService.selectDefaultAddressByUserId(userId);
        return success(address);
    }
}
