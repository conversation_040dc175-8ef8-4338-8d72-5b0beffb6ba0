package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppFunction;
import com.ruoyi.fuguang.service.IAppFunctionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP功能配置管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController("appFunctionManageController")
@RequestMapping("/fuguang/function")
public class AppFunctionManageController extends BaseController
{
    @Autowired
    private IAppFunctionService appFunctionService;

    /**
     * 查询APP功能配置列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:function:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppFunction appFunction)
    {
        startPage();
        List<AppFunction> list = appFunctionService.selectAppFunctionList(appFunction);
        return getDataTable(list);
    }

    /**
     * 导出APP功能配置列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:function:export')")
    @Log(title = "APP功能配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppFunction appFunction)
    {
        List<AppFunction> list = appFunctionService.selectAppFunctionList(appFunction);
        ExcelUtil<AppFunction> util = new ExcelUtil<AppFunction>(AppFunction.class);
        util.exportExcel(response, list, "APP功能配置数据");
    }

    /**
     * 获取APP功能配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:function:query')")
    @GetMapping(value = "/{functionId}")
    public AjaxResult getInfo(@PathVariable("functionId") Long functionId)
    {
        return success(appFunctionService.selectAppFunctionByFunctionId(functionId));
    }

    /**
     * 新增APP功能配置
     */
    @PreAuthorize("@ss.hasPermi('fuguang:function:add')")
    @Log(title = "APP功能配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppFunction appFunction)
    {
        appFunction.setCreateBy(getUsername());
        return toAjax(appFunctionService.insertAppFunction(appFunction));
    }

    /**
     * 修改APP功能配置
     */
    @PreAuthorize("@ss.hasPermi('fuguang:function:edit')")
    @Log(title = "APP功能配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppFunction appFunction)
    {
        appFunction.setUpdateBy(getUsername());
        return toAjax(appFunctionService.updateAppFunction(appFunction));
    }

    /**
     * 删除APP功能配置
     */
    @PreAuthorize("@ss.hasPermi('fuguang:function:remove')")
    @Log(title = "APP功能配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{functionIds}")
    public AjaxResult remove(@PathVariable Long[] functionIds)
    {
        return toAjax(appFunctionService.deleteAppFunctionByFunctionIds(functionIds));
    }

    /**
     * 获取启用的APP功能配置列表
     */
    @GetMapping("/enabled")
    public AjaxResult getEnabledFunctions()
    {
        List<AppFunction> list = appFunctionService.selectEnabledAppFunctionList();
        return success(list);
    }

    /**
     * 根据显示位置获取启用的APP功能配置列表
     */
    @GetMapping("/enabled/{displayLocation}")
    public AjaxResult getEnabledFunctionsByLocation(@PathVariable("displayLocation") String displayLocation)
    {
        List<AppFunction> list = appFunctionService.selectEnabledAppFunctionListByLocation(displayLocation);
        return success(list);
    }

    /**
     * 修改功能状态
     */
    @PreAuthorize("@ss.hasPermi('fuguang:function:edit')")
    @Log(title = "修改功能状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody AppFunction appFunction)
    {
        appFunction.setUpdateBy(getUsername());
        return toAjax(appFunctionService.updateAppFunction(appFunction));
    }
}
