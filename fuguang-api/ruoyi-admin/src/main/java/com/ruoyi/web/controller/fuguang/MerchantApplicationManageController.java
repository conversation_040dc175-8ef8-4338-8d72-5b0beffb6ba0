package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;

/**
 * 商家申请管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController("merchantApplicationManageController")
@RequestMapping("/fuguang/merchant")
public class MerchantApplicationManageController extends BaseController
{
    @Autowired
    private IMerchantApplicationService merchantApplicationService;

    /**
     * 查询商家申请列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:list')")
    @GetMapping("/list")
    public TableDataInfo list(MerchantApplication merchantApplication)
    {
        startPage();
        List<MerchantApplication> list = merchantApplicationService.selectMerchantApplicationList(merchantApplication);
        return getDataTable(list);
    }

    /**
     * 导出商家申请列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:export')")
    @Log(title = "商家申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MerchantApplication merchantApplication)
    {
        List<MerchantApplication> list = merchantApplicationService.selectMerchantApplicationList(merchantApplication);
        ExcelUtil<MerchantApplication> util = new ExcelUtil<MerchantApplication>(MerchantApplication.class);
        util.exportExcel(response, list, "商家申请数据");
    }

    /**
     * 获取商家申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:query')")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(merchantApplicationService.selectMerchantApplicationByApplicationId(applicationId));
    }

    /**
     * 新增商家申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:add')")
    @Log(title = "商家申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MerchantApplication merchantApplication)
    {
        // 数据校验
        String validateResult = merchantApplicationService.validateApplicationData(merchantApplication);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        // 检查用户是否可以申请
        if (!merchantApplicationService.checkUserCanApply(merchantApplication.getUserId()))
        {
            return error("该用户已经是商家用户或已有待审核的申请，无法重复申请");
        }
        
        merchantApplication.setCreateBy(getUsername());
        boolean result = merchantApplicationService.submitApplication(merchantApplication);
        return result ? success() : error("新增失败");
    }

    /**
     * 修改商家申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:edit')")
    @Log(title = "商家申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MerchantApplication merchantApplication)
    {
        // 数据校验
        String validateResult = merchantApplicationService.validateApplicationData(merchantApplication);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        merchantApplication.setUpdateBy(getUsername());
        return toAjax(merchantApplicationService.updateMerchantApplication(merchantApplication));
    }

    /**
     * 删除商家申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:remove')")
    @Log(title = "商家申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds)
    {
        return toAjax(merchantApplicationService.deleteMerchantApplicationByApplicationIds(applicationIds));
    }

    /**
     * 审核商家申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:audit')")
    @Log(title = "商家申请审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult auditApplication(@RequestBody MerchantApplication merchantApplication)
    {
        if (merchantApplication.getApplicationId() == null)
        {
            return error("申请ID不能为空");
        }
        
        if (StringUtils.isEmpty(merchantApplication.getApplicationStatus()))
        {
            return error("审核状态不能为空");
        }
        
        if (!"1".equals(merchantApplication.getApplicationStatus()) && !"2".equals(merchantApplication.getApplicationStatus()))
        {
            return error("审核状态只能是通过(1)或拒绝(2)");
        }
        
        boolean result = merchantApplicationService.auditApplication(
            merchantApplication.getApplicationId(),
            merchantApplication.getApplicationStatus(),
            merchantApplication.getAuditRemark(),
            getUsername()
        );
        
        if (result)
        {
            String statusText = "1".equals(merchantApplication.getApplicationStatus()) ? "通过" : "拒绝";
            return success("审核" + statusText + "成功");
        }
        return error("审核失败");
    }

    /**
     * 批量审核商家申请
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:audit')")
    @Log(title = "商家申请批量审核", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAudit")
    public AjaxResult batchAuditApplication(@RequestBody MerchantApplication merchantApplication)
    {
        if (merchantApplication.getApplicationIds() == null || merchantApplication.getApplicationIds().length == 0)
        {
            return error("申请ID不能为空");
        }
        
        if (StringUtils.isEmpty(merchantApplication.getApplicationStatus()))
        {
            return error("审核状态不能为空");
        }
        
        if (!"1".equals(merchantApplication.getApplicationStatus()) && !"2".equals(merchantApplication.getApplicationStatus()))
        {
            return error("审核状态只能是通过(1)或拒绝(2)");
        }
        
        int successCount = 0;
        for (Long applicationId : merchantApplication.getApplicationIds())
        {
            boolean result = merchantApplicationService.auditApplication(
                applicationId,
                merchantApplication.getApplicationStatus(),
                merchantApplication.getAuditRemark(),
                getUsername()
            );
            if (result)
            {
                successCount++;
            }
        }
        
        String statusText = "1".equals(merchantApplication.getApplicationStatus()) ? "通过" : "拒绝";
        return success("批量审核完成，成功" + statusText + successCount + "个申请");
    }

    /**
     * 获取待审核申请数量
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchant:list')")
    @GetMapping("/pendingCount")
    public AjaxResult getPendingCount()
    {
        MerchantApplication query = new MerchantApplication();
        query.setApplicationStatus("0"); // 待审核
        List<MerchantApplication> list = merchantApplicationService.selectMerchantApplicationList(query);
        return success(list.size());
    }
}
