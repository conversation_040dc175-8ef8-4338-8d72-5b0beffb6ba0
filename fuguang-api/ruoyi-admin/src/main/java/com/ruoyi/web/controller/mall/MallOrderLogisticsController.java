package com.ruoyi.web.controller.mall;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.fuguang.domain.MallOrderLogistics;
import com.ruoyi.fuguang.service.IMallOrderLogisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订单物流Controller
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/mall/logistics")
public class MallOrderLogisticsController extends BaseController
{
    @Autowired
    private IMallOrderLogisticsService mallOrderLogisticsService;

    /**
     * 查询订单物流列表
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:list')")
    @GetMapping("/list")
    public TableDataInfo list(MallOrderLogistics mallOrderLogistics)
    {
        startPage();
        List<MallOrderLogistics> list = mallOrderLogisticsService.selectMallOrderLogisticsList(mallOrderLogistics);
        return getDataTable(list);
    }

    /**
     * 根据订单ID查询物流信息
     */
    @GetMapping("/listByOrder/{orderId}")
    public AjaxResult listByOrder(@PathVariable("orderId") Long orderId)
    {
        List<MallOrderLogistics> list = mallOrderLogisticsService.selectMallOrderLogisticsByOrderId(orderId);
        return success(list);
    }

    /**
     * 根据订单号查询物流信息
     */
    @GetMapping("/listByOrderNo/{orderNo}")
    public AjaxResult listByOrderNo(@PathVariable("orderNo") String orderNo)
    {
        List<MallOrderLogistics> list = mallOrderLogisticsService.selectMallOrderLogisticsByOrderNo(orderNo);
        return success(list);
    }

    /**
     * 导出订单物流列表
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:export')")
    @Log(title = "订单物流", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MallOrderLogistics mallOrderLogistics)
    {
        List<MallOrderLogistics> list = mallOrderLogisticsService.selectMallOrderLogisticsList(mallOrderLogistics);
        ExcelUtil<MallOrderLogistics> util = new ExcelUtil<MallOrderLogistics>(MallOrderLogistics.class);
        util.exportExcel(response, list, "订单物流数据");
    }

    /**
     * 获取订单物流详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:query')")
    @GetMapping(value = "/{logisticsId}")
    public AjaxResult getInfo(@PathVariable("logisticsId") Long logisticsId)
    {
        return success(mallOrderLogisticsService.selectMallOrderLogisticsByLogisticsId(logisticsId));
    }

    /**
     * 新增订单物流
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:add')")
    @Log(title = "订单物流", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MallOrderLogistics mallOrderLogistics)
    {
        return toAjax(mallOrderLogisticsService.insertMallOrderLogistics(mallOrderLogistics));
    }

    /**
     * 修改订单物流
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:edit')")
    @Log(title = "订单物流", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MallOrderLogistics mallOrderLogistics)
    {
        return toAjax(mallOrderLogisticsService.updateMallOrderLogistics(mallOrderLogistics));
    }

    /**
     * 删除订单物流
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:remove')")
    @Log(title = "订单物流", businessType = BusinessType.DELETE)
	@DeleteMapping("/{logisticsIds}")
    public AjaxResult remove(@PathVariable Long[] logisticsIds)
    {
        return toAjax(mallOrderLogisticsService.deleteMallOrderLogisticsByLogisticsIds(logisticsIds));
    }

    /**
     * 创建发货物流记录
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:delivery')")
    @Log(title = "订单发货", businessType = BusinessType.INSERT)
    @PostMapping("/delivery")
    public AjaxResult createDelivery(@RequestBody MallOrderLogistics mallOrderLogistics)
    {
        return toAjax(mallOrderLogisticsService.createDeliveryLogistics(
            mallOrderLogistics.getOrderId(),
            mallOrderLogistics.getOrderNo(),
            mallOrderLogistics.getLogisticsCompany(),
            mallOrderLogistics.getLogisticsNo()
        ));
    }

    /**
     * 更新物流状态
     */
    @PreAuthorize("@ss.hasPermi('mall:logistics:status')")
    @Log(title = "物流状态更新", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody MallOrderLogistics mallOrderLogistics)
    {
        return toAjax(mallOrderLogisticsService.updateLogisticsStatus(
            mallOrderLogistics.getLogisticsId(),
            mallOrderLogistics.getLogisticsStatus(),
            mallOrderLogistics.getLogisticsInfo()
        ));
    }
}
