package com.ruoyi.web.controller.mall;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.MallProduct;
import com.ruoyi.fuguang.service.IMallProductService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商品信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/mall/product")
public class MallProductController extends BaseController
{
    @Autowired
    private IMallProductService mallProductService;

    /**
     * 查询商品信息列表
     */
    @PreAuthorize("@ss.hasPermi('mall:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(MallProduct mallProduct)
    {
        startPage();
        List<MallProduct> list = mallProductService.selectMallProductList(mallProduct);
        return getDataTable(list);
    }

    /**
     * 导出商品信息列表
     */
    @PreAuthorize("@ss.hasPermi('mall:product:export')")
    @Log(title = "商品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MallProduct mallProduct)
    {
        List<MallProduct> list = mallProductService.selectMallProductList(mallProduct);
        ExcelUtil<MallProduct> util = new ExcelUtil<MallProduct>(MallProduct.class);
        util.exportExcel(response, list, "商品信息数据");
    }

    /**
     * 获取商品信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:product:query')")
    @GetMapping(value = "/{productId}")
    public AjaxResult getInfo(@PathVariable("productId") Long productId)
    {
        return success(mallProductService.selectMallProductByProductId(productId));
    }

    /**
     * 新增商品信息
     */
    @PreAuthorize("@ss.hasPermi('mall:product:add')")
    @Log(title = "商品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MallProduct mallProduct)
    {
        if ("1".equals(mallProductService.checkProductNameUnique(mallProduct)))
        {
            return error("新增商品'" + mallProduct.getProductName() + "'失败，商品名称已存在");
        }
        mallProduct.setCreateBy(getUsername());
        return toAjax(mallProductService.insertMallProduct(mallProduct));
    }

    /**
     * 修改商品信息
     */
    @PreAuthorize("@ss.hasPermi('mall:product:edit')")
    @Log(title = "商品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MallProduct mallProduct)
    {
        if ("1".equals(mallProductService.checkProductNameUnique(mallProduct)))
        {
            return error("修改商品'" + mallProduct.getProductName() + "'失败，商品名称已存在");
        }
        mallProduct.setUpdateBy(getUsername());
        return toAjax(mallProductService.updateMallProduct(mallProduct));
    }

    /**
     * 删除商品信息
     */
    @PreAuthorize("@ss.hasPermi('mall:product:remove')")
    @Log(title = "商品信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{productIds}")
    public AjaxResult remove(@PathVariable Long[] productIds)
    {
        return toAjax(mallProductService.deleteMallProductByProductIds(productIds));
    }

    /**
     * 上架商品
     */
    @PreAuthorize("@ss.hasPermi('mall:product:edit')")
    @Log(title = "商品上架", businessType = BusinessType.UPDATE)
    @PutMapping("/onShelf/{productId}")
    public AjaxResult onShelf(@PathVariable Long productId)
    {
        return toAjax(mallProductService.onShelfProduct(productId));
    }

    /**
     * 下架商品
     */
    @PreAuthorize("@ss.hasPermi('mall:product:edit')")
    @Log(title = "商品下架", businessType = BusinessType.UPDATE)
    @PutMapping("/offShelf/{productId}")
    public AjaxResult offShelf(@PathVariable Long productId)
    {
        return toAjax(mallProductService.offShelfProduct(productId));
    }

    /**
     * 批量上架商品
     */
    @PreAuthorize("@ss.hasPermi('mall:product:edit')")
    @Log(title = "批量上架商品", businessType = BusinessType.UPDATE)
    @PutMapping("/batchOnShelf")
    public AjaxResult batchOnShelf(@RequestBody Long[] productIds)
    {
        return toAjax(mallProductService.batchOnShelfProduct(productIds));
    }

    /**
     * 批量下架商品
     */
    @PreAuthorize("@ss.hasPermi('mall:product:edit')")
    @Log(title = "批量下架商品", businessType = BusinessType.UPDATE)
    @PutMapping("/batchOffShelf")
    public AjaxResult batchOffShelf(@RequestBody Long[] productIds)
    {
        return toAjax(mallProductService.batchOffShelfProduct(productIds));
    }
}
