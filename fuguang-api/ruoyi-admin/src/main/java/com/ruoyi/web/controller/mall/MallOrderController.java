package com.ruoyi.web.controller.mall;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.service.IMallOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 订单Controller
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/mall/order")
public class MallOrderController extends BaseController
{
    @Autowired
    private IMallOrderService mallOrderService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('mall:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(MallOrder mallOrder)
    {
        startPage();
        List<MallOrder> list = mallOrderService.selectMallOrderList(mallOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('mall:order:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MallOrder mallOrder)
    {
        List<MallOrder> list = mallOrderService.selectMallOrderList(mallOrder);
        ExcelUtil<MallOrder> util = new ExcelUtil<MallOrder>(MallOrder.class);
        util.exportExcel(response, list, "订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:order:detail')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(mallOrderService.selectMallOrderByOrderId(orderId));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('mall:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MallOrder mallOrder)
    {
        mallOrder.setUpdateBy(getUsername());
        return toAjax(mallOrderService.updateMallOrder(mallOrder));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('mall:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds)
    {
        return toAjax(mallOrderService.deleteMallOrderByOrderIds(orderIds));
    }

    /**
     * 发货
     */
    @PreAuthorize("@ss.hasPermi('mall:order:delivery')")
    @Log(title = "订单发货", businessType = BusinessType.UPDATE)
    @PutMapping("/delivery/{orderId}")
    public AjaxResult delivery(@PathVariable Long orderId)
    {
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!"1".equals(order.getOrderStatus())) {
            return error("订单状态不正确，无法发货");
        }
        
        int result = mallOrderService.deliverOrder(orderId);
        if (result > 0) {
            return success("发货成功");
        } else {
            return error("发货失败");
        }
    }

    /**
     * 取消订单
     */
    @PreAuthorize("@ss.hasPermi('mall:order:cancel')")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{orderId}")
    public AjaxResult cancel(@PathVariable Long orderId)
    {
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!"0".equals(order.getOrderStatus()) && !"1".equals(order.getOrderStatus())) {
            return error("订单状态不正确，无法取消");
        }
        
        int result = mallOrderService.cancelOrder(orderId, null);
        if (result > 0) {
            return success("订单取消成功");
        } else {
            return error("订单取消失败");
        }
    }
}
