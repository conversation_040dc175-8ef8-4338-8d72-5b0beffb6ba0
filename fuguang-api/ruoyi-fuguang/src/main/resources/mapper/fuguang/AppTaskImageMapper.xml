<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppTaskImageMapper">
    
    <resultMap type="AppTaskImage" id="AppTaskImageResult">
        <id     property="imageId"      column="image_id"    />
        <result property="taskId"       column="task_id"     />
        <result property="imageUrl"     column="image_url"   />
        <result property="imageName"    column="image_name"  />
        <result property="imageSize"    column="image_size"  />
        <result property="sortOrder"    column="sort_order"  />
        <result property="createTime"   column="create_time" />
    </resultMap>

    <sql id="selectAppTaskImageVo">
        select image_id, task_id, image_url, image_name, image_size, sort_order, create_time from app_task_image
    </sql>

    <select id="selectAppTaskImageList" parameterType="AppTaskImage" resultMap="AppTaskImageResult">
        <include refid="selectAppTaskImageVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="imageName != null  and imageName != ''"> and image_name like concat('%', #{imageName}, '%')</if>
        </where>
        order by sort_order asc, create_time asc
    </select>
    
    <select id="selectAppTaskImageByImageId" parameterType="Long" resultMap="AppTaskImageResult">
        <include refid="selectAppTaskImageVo"/>
        where image_id = #{imageId}
    </select>
    
    <select id="selectAppTaskImagesByTaskId" parameterType="Long" resultMap="AppTaskImageResult">
        <include refid="selectAppTaskImageVo"/>
        where task_id = #{taskId}
        order by sort_order asc, create_time asc
    </select>
        
    <insert id="insertAppTaskImage" parameterType="AppTaskImage" useGeneratedKeys="true" keyProperty="imageId">
        insert into app_task_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="imageName != null">image_name,</if>
            <if test="imageSize != null">image_size,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="imageName != null">#{imageName},</if>
            <if test="imageSize != null">#{imageSize},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <insert id="insertAppTaskImageBatch" parameterType="java.util.List">
        insert into app_task_image(task_id, image_url, image_name, image_size, sort_order, create_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.taskId}, #{item.imageUrl}, #{item.imageName}, #{item.imageSize}, #{item.sortOrder}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateAppTaskImage" parameterType="AppTaskImage">
        update app_task_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="imageName != null">image_name = #{imageName},</if>
            <if test="imageSize != null">image_size = #{imageSize},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
        </trim>
        where image_id = #{imageId}
    </update>

    <delete id="deleteAppTaskImageByImageId" parameterType="Long">
        delete from app_task_image where image_id = #{imageId}
    </delete>

    <delete id="deleteAppTaskImageByImageIds" parameterType="String">
        delete from app_task_image where image_id in 
        <foreach item="imageId" collection="array" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>
    
    <delete id="deleteAppTaskImagesByTaskId" parameterType="Long">
        delete from app_task_image where task_id = #{taskId}
    </delete>

</mapper>
