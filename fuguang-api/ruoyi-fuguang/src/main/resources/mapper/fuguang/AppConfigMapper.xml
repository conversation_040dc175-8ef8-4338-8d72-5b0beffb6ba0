<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppConfigMapper">
    
    <resultMap type="AppConfig" id="AppConfigResult">
        <id     property="configId"    column="config_id"    />
        <result property="configName"  column="config_name"  />
        <result property="configKey"   column="config_key"   />
        <result property="configValue" column="config_value" />
        <result property="configImage" column="config_image" />
        <result property="configType"  column="config_type"  />
        <result property="configDesc"  column="config_desc"  />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"  column="create_time"  />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"  column="update_time"  />
        <result property="remark"      column="remark"       />
    </resultMap>

    <sql id="selectAppConfigVo">
        select config_id, config_name, config_key, config_value, config_image, config_type, config_desc, create_by, create_time, update_by, update_time, remark from app_config
    </sql>

    <select id="selectAppConfigList" parameterType="AppConfig" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        <where>  
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="configKey != null  and configKey != ''"> and config_key like concat('%', #{configKey}, '%')</if>
            <if test="configType != null  and configType != ''"> and config_type = #{configType}</if>
        </where>
        order by config_id
    </select>
    
    <select id="selectAppConfigByConfigId" parameterType="Long" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        where config_id = #{configId}
    </select>
    
    <select id="selectAppConfigByKey" parameterType="String" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        where config_key = #{configKey}
    </select>
    
    <select id="checkConfigKeyUnique" parameterType="String" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        where config_key = #{configKey} limit 1
    </select>
        
    <insert id="insertAppConfig" parameterType="AppConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into app_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name,</if>
            <if test="configKey != null and configKey != ''">config_key,</if>
            <if test="configValue != null">config_value,</if>
            <if test="configImage != null">config_image,</if>
            <if test="configType != null">config_type,</if>
            <if test="configDesc != null">config_desc,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">#{configName},</if>
            <if test="configKey != null and configKey != ''">#{configKey},</if>
            <if test="configValue != null">#{configValue},</if>
            <if test="configImage != null">#{configImage},</if>
            <if test="configType != null">#{configType},</if>
            <if test="configDesc != null">#{configDesc},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppConfig" parameterType="AppConfig">
        update app_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name = #{configName},</if>
            <if test="configKey != null and configKey != ''">config_key = #{configKey},</if>
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="configImage != null">config_image = #{configImage},</if>
            <if test="configType != null">config_type = #{configType},</if>
            <if test="configDesc != null">config_desc = #{configDesc},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteAppConfigByConfigId" parameterType="Long">
        delete from app_config where config_id = #{configId}
    </delete>

    <delete id="deleteAppConfigByConfigIds" parameterType="String">
        delete from app_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

</mapper>
