<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallOrderItemMapper">

    <resultMap type="MallOrderItem" id="MallOrderItemResult">
        <id     property="itemId"        column="item_id"        />
        <result property="orderId"       column="order_id"       />
        <result property="productId"     column="product_id"     />
        <result property="specId"        column="spec_id"        />
        <result property="productName"   column="product_name"   />
        <result property="specName"      column="spec_name"      />
        <result property="productImage"  column="product_image"  />
        <result property="specImage"     column="spec_image"     />
        <result property="productPrice"  column="product_price"  />
        <result property="quantity"      column="quantity"       />
        <result property="totalPrice"    column="total_price"    />
    </resultMap>

    <sql id="selectMallOrderItemVo">
        select item_id, order_id, product_id, spec_id, product_name, spec_name, product_image, spec_image, product_price, quantity, total_price from mall_order_item
    </sql>

    <select id="selectMallOrderItemList" parameterType="MallOrderItem" resultMap="MallOrderItemResult">
        <include refid="selectMallOrderItemVo"/>
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="specId != null "> and spec_id = #{specId}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
        </where>
        order by item_id
    </select>

    <select id="selectMallOrderItemByItemId" parameterType="Long" resultMap="MallOrderItemResult">
        <include refid="selectMallOrderItemVo"/>
        where item_id = #{itemId}
    </select>

    <select id="selectOrderItemsByOrderId" parameterType="Long" resultMap="MallOrderItemResult">
        <include refid="selectMallOrderItemVo"/>
        where order_id = #{orderId}
        order by item_id
    </select>

    <insert id="insertMallOrderItem" parameterType="MallOrderItem" useGeneratedKeys="true" keyProperty="itemId">
        insert into mall_order_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="specId != null">spec_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="specName != null">spec_name,</if>
            <if test="productImage != null">product_image,</if>
            <if test="specImage != null">spec_image,</if>
            <if test="productPrice != null">product_price,</if>
            <if test="quantity != null">quantity,</if>
            <if test="totalPrice != null">total_price,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="specId != null">#{specId},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="specName != null">#{specName},</if>
            <if test="productImage != null">#{productImage},</if>
            <if test="specImage != null">#{specImage},</if>
            <if test="productPrice != null">#{productPrice},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
         </trim>
    </insert>

    <insert id="batchInsertOrderItems" parameterType="java.util.List">
        insert into mall_order_item(order_id, product_id, spec_id, product_name, spec_name, product_image, spec_image, product_price, quantity, total_price)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.orderId}, #{item.productId}, #{item.specId}, #{item.productName}, #{item.specName}, #{item.productImage}, #{item.specImage}, #{item.productPrice}, #{item.quantity}, #{item.totalPrice})
        </foreach>
    </insert>

    <update id="updateMallOrderItem" parameterType="MallOrderItem">
        update mall_order_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="specId != null">spec_id = #{specId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="specName != null">spec_name = #{specName},</if>
            <if test="productImage != null">product_image = #{productImage},</if>
            <if test="specImage != null">spec_image = #{specImage},</if>
            <if test="productPrice != null">product_price = #{productPrice},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
        </trim>
        where item_id = #{itemId}
    </update>

    <delete id="deleteMallOrderItemByItemId" parameterType="Long">
        delete from mall_order_item where item_id = #{itemId}
    </delete>

    <delete id="deleteMallOrderItemByItemIds" parameterType="String">
        delete from mall_order_item where item_id in
        <foreach item="itemId" collection="array" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </delete>

    <delete id="deleteOrderItemsByOrderId" parameterType="Long">
        delete from mall_order_item where order_id = #{orderId}
    </delete>

</mapper>