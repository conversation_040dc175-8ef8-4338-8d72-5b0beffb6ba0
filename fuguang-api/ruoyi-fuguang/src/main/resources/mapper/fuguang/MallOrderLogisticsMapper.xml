<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallOrderLogisticsMapper">
    
    <resultMap type="MallOrderLogistics" id="MallOrderLogisticsResult">
        <result property="logisticsId"    column="logistics_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="logisticsType"    column="logistics_type"    />
        <result property="logisticsCompany"    column="logistics_company"    />
        <result property="logisticsNo"    column="logistics_no"    />
        <result property="logisticsStatus"    column="logistics_status"    />
        <result property="senderName"    column="sender_name"    />
        <result property="senderPhone"    column="sender_phone"    />
        <result property="senderAddress"    column="sender_address"    />
        <result property="receiverName"    column="receiver_name"    />
        <result property="receiverPhone"    column="receiver_phone"    />
        <result property="receiverAddress"    column="receiver_address"    />
        <result property="sendTime"    column="send_time"    />
        <result property="receiveTime"    column="receive_time"    />
        <result property="logisticsInfo"    column="logistics_info"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMallOrderLogisticsVo">
        select logistics_id, order_id, order_no, logistics_type, logistics_company, logistics_no,
               logistics_status, sender_name, sender_phone, sender_address, receiver_name, receiver_phone,
               receiver_address, send_time, receive_time, logistics_info, create_time, update_time, remark
        from mall_order_logistics
    </sql>

    <select id="selectMallOrderLogisticsList" parameterType="MallOrderLogistics" resultMap="MallOrderLogisticsResult">
        <include refid="selectMallOrderLogisticsVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="logisticsType != null  and logisticsType != ''"> and logistics_type = #{logisticsType}</if>
            <if test="logisticsCompany != null  and logisticsCompany != ''"> and logistics_company like concat('%', #{logisticsCompany}, '%')</if>
            <if test="logisticsNo != null  and logisticsNo != ''"> and logistics_no like concat('%', #{logisticsNo}, '%')</if>
            <if test="logisticsStatus != null  and logisticsStatus != ''"> and logistics_status = #{logisticsStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMallOrderLogisticsByLogisticsId" parameterType="Long" resultMap="MallOrderLogisticsResult">
        <include refid="selectMallOrderLogisticsVo"/>
        where logistics_id = #{logisticsId}
    </select>

    <select id="selectMallOrderLogisticsByOrderId" parameterType="Long" resultMap="MallOrderLogisticsResult">
        <include refid="selectMallOrderLogisticsVo"/>
        where order_id = #{orderId}
        order by create_time desc
    </select>

    <select id="selectMallOrderLogisticsByOrderNo" parameterType="String" resultMap="MallOrderLogisticsResult">
        <include refid="selectMallOrderLogisticsVo"/>
        where order_no = #{orderNo}
        order by create_time desc
    </select>
        
    <insert id="insertMallOrderLogistics" parameterType="MallOrderLogistics" useGeneratedKeys="true" keyProperty="logisticsId">
        insert into mall_order_logistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="logisticsType != null">logistics_type,</if>
            <if test="logisticsCompany != null">logistics_company,</if>
            <if test="logisticsNo != null">logistics_no,</if>
            <if test="logisticsStatus != null">logistics_status,</if>
            <if test="senderName != null">sender_name,</if>
            <if test="senderPhone != null">sender_phone,</if>
            <if test="senderAddress != null">sender_address,</if>
            <if test="receiverName != null">receiver_name,</if>
            <if test="receiverPhone != null">receiver_phone,</if>
            <if test="receiverAddress != null">receiver_address,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="logisticsInfo != null">logistics_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="logisticsType != null">#{logisticsType},</if>
            <if test="logisticsCompany != null">#{logisticsCompany},</if>
            <if test="logisticsNo != null">#{logisticsNo},</if>
            <if test="logisticsStatus != null">#{logisticsStatus},</if>
            <if test="senderName != null">#{senderName},</if>
            <if test="senderPhone != null">#{senderPhone},</if>
            <if test="senderAddress != null">#{senderAddress},</if>
            <if test="receiverName != null">#{receiverName},</if>
            <if test="receiverPhone != null">#{receiverPhone},</if>
            <if test="receiverAddress != null">#{receiverAddress},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="logisticsInfo != null">#{logisticsInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMallOrderLogistics" parameterType="MallOrderLogistics">
        update mall_order_logistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="logisticsType != null">logistics_type = #{logisticsType},</if>
            <if test="logisticsCompany != null">logistics_company = #{logisticsCompany},</if>
            <if test="logisticsNo != null">logistics_no = #{logisticsNo},</if>
            <if test="logisticsStatus != null">logistics_status = #{logisticsStatus},</if>
            <if test="senderName != null">sender_name = #{senderName},</if>
            <if test="senderPhone != null">sender_phone = #{senderPhone},</if>
            <if test="senderAddress != null">sender_address = #{senderAddress},</if>
            <if test="receiverName != null">receiver_name = #{receiverName},</if>
            <if test="receiverPhone != null">receiver_phone = #{receiverPhone},</if>
            <if test="receiverAddress != null">receiver_address = #{receiverAddress},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="logisticsInfo != null">logistics_info = #{logisticsInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where logistics_id = #{logisticsId}
    </update>

    <delete id="deleteMallOrderLogisticsByLogisticsId" parameterType="Long">
        delete from mall_order_logistics where logistics_id = #{logisticsId}
    </delete>

    <delete id="deleteMallOrderLogisticsByLogisticsIds" parameterType="String">
        delete from mall_order_logistics where logistics_id in 
        <foreach item="logisticsId" collection="array" open="(" separator="," close=")">
            #{logisticsId}
        </foreach>
    </delete>
</mapper>
