<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.CommissionBillMapper">
    
    <resultMap type="CommissionBill" id="CommissionBillResult">
        <result property="billId"    column="bill_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="billYear"    column="bill_year"    />
        <result property="billMonth"    column="bill_month"    />
        <result property="totalIncome"    column="total_income"    />
        <result property="taskCommission"    column="task_commission"    />
        <result property="recommendReward"    column="recommend_reward"    />
        <result property="otherIncome"    column="other_income"    />
        <result property="totalWithdraw"    column="total_withdraw"    />
        <result property="withdrawCount"    column="withdraw_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCommissionBillVo">
        select bill_id, user_id, user_name, bill_year, bill_month, total_income, task_commission, recommend_reward, other_income, total_withdraw, withdraw_count, create_time, update_time from commission_bill
    </sql>

    <select id="selectCommissionBillList" parameterType="CommissionBill" resultMap="CommissionBillResult">
        <include refid="selectCommissionBillVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="billYear != null "> and bill_year = #{billYear}</if>
            <if test="billMonth != null "> and bill_month = #{billMonth}</if>
        </where>
        order by bill_year desc, bill_month desc
    </select>
    
    <select id="selectCommissionBillByBillId" parameterType="Long" resultMap="CommissionBillResult">
        <include refid="selectCommissionBillVo"/>
        where bill_id = #{billId}
    </select>
    
    <select id="selectCommissionBillListByUserId" parameterType="Long" resultMap="CommissionBillResult">
        <include refid="selectCommissionBillVo"/>
        where user_id = #{userId}
        order by bill_year desc, bill_month desc
    </select>
    
    <select id="selectCommissionBillByUserIdAndMonth" resultMap="CommissionBillResult">
        <include refid="selectCommissionBillVo"/>
        where user_id = #{userId} and bill_year = #{billYear} and bill_month = #{billMonth}
    </select>
        
    <insert id="insertCommissionBill" parameterType="CommissionBill" useGeneratedKeys="true" keyProperty="billId">
        insert into commission_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="billYear != null">bill_year,</if>
            <if test="billMonth != null">bill_month,</if>
            <if test="totalIncome != null">total_income,</if>
            <if test="taskCommission != null">task_commission,</if>
            <if test="recommendReward != null">recommend_reward,</if>
            <if test="otherIncome != null">other_income,</if>
            <if test="totalWithdraw != null">total_withdraw,</if>
            <if test="withdrawCount != null">withdraw_count,</if>
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="billYear != null">#{billYear},</if>
            <if test="billMonth != null">#{billMonth},</if>
            <if test="totalIncome != null">#{totalIncome},</if>
            <if test="taskCommission != null">#{taskCommission},</if>
            <if test="recommendReward != null">#{recommendReward},</if>
            <if test="otherIncome != null">#{otherIncome},</if>
            <if test="totalWithdraw != null">#{totalWithdraw},</if>
            <if test="withdrawCount != null">#{withdrawCount},</if>
            now(),
            now()
         </trim>
    </insert>

    <update id="updateCommissionBill" parameterType="CommissionBill">
        update commission_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="billYear != null">bill_year = #{billYear},</if>
            <if test="billMonth != null">bill_month = #{billMonth},</if>
            <if test="totalIncome != null">total_income = #{totalIncome},</if>
            <if test="taskCommission != null">task_commission = #{taskCommission},</if>
            <if test="recommendReward != null">recommend_reward = #{recommendReward},</if>
            <if test="otherIncome != null">other_income = #{otherIncome},</if>
            <if test="totalWithdraw != null">total_withdraw = #{totalWithdraw},</if>
            <if test="withdrawCount != null">withdraw_count = #{withdrawCount},</if>
            update_time = now()
        </trim>
        where bill_id = #{billId}
    </update>

    <delete id="deleteCommissionBillByBillId" parameterType="Long">
        delete from commission_bill where bill_id = #{billId}
    </delete>

    <delete id="deleteCommissionBillByBillIds" parameterType="String">
        delete from commission_bill where bill_id in 
        <foreach item="billId" collection="array" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </delete>
    
    <update id="updateTaskCommission">
        update commission_bill set 
            task_commission = task_commission + #{amount},
            total_income = total_income + #{amount},
            update_time = now()
        where user_id = #{userId} and bill_year = #{billYear} and bill_month = #{billMonth}
    </update>
    
    <update id="updateRecommendReward">
        update commission_bill set 
            recommend_reward = recommend_reward + #{amount},
            total_income = total_income + #{amount},
            update_time = now()
        where user_id = #{userId} and bill_year = #{billYear} and bill_month = #{billMonth}
    </update>
    
    <update id="updateOtherIncome">
        update commission_bill set 
            other_income = other_income + #{amount},
            total_income = total_income + #{amount},
            update_time = now()
        where user_id = #{userId} and bill_year = #{billYear} and bill_month = #{billMonth}
    </update>
    
    <update id="updateWithdrawAmount">
        update commission_bill set 
            total_withdraw = total_withdraw + #{amount},
            withdraw_count = withdraw_count + 1,
            update_time = now()
        where user_id = #{userId} and bill_year = #{billYear} and bill_month = #{billMonth}
    </update>

</mapper>
