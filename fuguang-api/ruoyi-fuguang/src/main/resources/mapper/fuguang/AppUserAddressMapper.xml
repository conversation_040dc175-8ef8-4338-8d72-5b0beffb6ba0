<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppUserAddressMapper">
    
    <resultMap type="AppUserAddress" id="AppUserAddressResult">
        <id     property="addressId"       column="address_id"      />
        <result property="userId"          column="user_id"         />
        <result property="contactName"     column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"   />
        <result property="contactSex"      column="contact_sex"     />
        <result property="province"        column="province"        />
        <result property="city"            column="city"            />
        <result property="district"        column="district"        />
        <result property="address"         column="address"         />
        <result property="fullAddress"     column="full_address"    />
        <result property="longitude"       column="longitude"       />
        <result property="latitude"        column="latitude"        />
        <result property="isDefault"       column="is_default"      />
        <result property="status"          column="status"          />
        <result property="delFlag"         column="del_flag"        />
        <result property="createBy"        column="create_by"       />
        <result property="createTime"      column="create_time"     />
        <result property="updateBy"        column="update_by"       />
        <result property="updateTime"      column="update_time"     />
        <result property="remark"          column="remark"          />
        <result property="userNickName"    column="user_nick_name"  />
        <result property="userPhone"       column="user_phone"      />
    </resultMap>

    <sql id="selectAppUserAddressVo">
        select a.address_id, a.user_id, a.contact_name, a.contact_phone, a.contact_sex, 
               a.province, a.city, a.district, a.address, a.full_address, 
               a.longitude, a.latitude, a.is_default, a.status, a.del_flag, 
               a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
               u.nick_name as user_nick_name, u.phonenumber as user_phone
        from app_user_address a
        left join app_user u on a.user_id = u.user_id
    </sql>

    <select id="selectAppUserAddressList" parameterType="AppUserAddress" resultMap="AppUserAddressResult">
        <include refid="selectAppUserAddressVo"/>
        <where>  
            a.del_flag = '0'
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="contactName != null  and contactName != ''"> and a.contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and a.contact_phone = #{contactPhone}</if>
            <if test="contactSex != null  and contactSex != ''"> and a.contact_sex = #{contactSex}</if>
            <if test="province != null  and province != ''"> and a.province like concat('%', #{province}, '%')</if>
            <if test="city != null  and city != ''"> and a.city like concat('%', #{city}, '%')</if>
            <if test="district != null  and district != ''"> and a.district like concat('%', #{district}, '%')</if>
            <if test="address != null  and address != ''"> and a.address like concat('%', #{address}, '%')</if>
            <if test="isDefault != null  and isDefault != ''"> and a.is_default = #{isDefault}</if>
            <if test="status != null  and status != ''"> and a.status = #{status}</if>
        </where>
        order by a.is_default desc, a.create_time desc
    </select>
    
    <select id="selectAppUserAddressByAddressId" parameterType="Long" resultMap="AppUserAddressResult">
        <include refid="selectAppUserAddressVo"/>
        where a.address_id = #{addressId} and a.del_flag = '0'
    </select>

    <select id="selectAppUserAddressListByUserId" parameterType="Long" resultMap="AppUserAddressResult">
        <include refid="selectAppUserAddressVo"/>
        where a.user_id = #{userId} and a.del_flag = '0' and a.status = '0'
        order by a.is_default desc, a.create_time desc
    </select>

    <select id="selectDefaultAddressByUserId" parameterType="Long" resultMap="AppUserAddressResult">
        <include refid="selectAppUserAddressVo"/>
        where a.user_id = #{userId} and a.is_default = '1' and a.del_flag = '0' and a.status = '0'
        limit 1
    </select>

    <select id="checkContactPhoneUnique" parameterType="AppUserAddress" resultMap="AppUserAddressResult">
        <include refid="selectAppUserAddressVo"/>
        where a.user_id = #{userId} and a.contact_phone = #{contactPhone} and a.del_flag = '0'
        <if test="addressId != null and addressId != 0">
            and a.address_id != #{addressId}
        </if>
        limit 1
    </select>
        
    <insert id="insertAppUserAddress" parameterType="AppUserAddress" useGeneratedKeys="true" keyProperty="addressId">
        insert into app_user_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactSex != null">contact_sex,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="fullAddress != null">full_address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactSex != null">#{contactSex},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="fullAddress != null">#{fullAddress},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppUserAddress" parameterType="AppUserAddress">
        update app_user_address
        <trim prefix="SET" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactSex != null">contact_sex = #{contactSex},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="fullAddress != null">full_address = #{fullAddress},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where address_id = #{addressId}
    </update>

    <update id="cancelDefaultAddressByUserId" parameterType="Long">
        update app_user_address set is_default = '0' where user_id = #{userId} and del_flag = '0'
    </update>

    <delete id="deleteAppUserAddressByAddressId" parameterType="Long">
        update app_user_address set del_flag = '2' where address_id = #{addressId}
    </delete>

    <delete id="deleteAppUserAddressByAddressIds" parameterType="String">
        update app_user_address set del_flag = '2' where address_id in 
        <foreach item="addressId" collection="array" open="(" separator="," close=")">
            #{addressId}
        </foreach>
    </delete>
</mapper>
