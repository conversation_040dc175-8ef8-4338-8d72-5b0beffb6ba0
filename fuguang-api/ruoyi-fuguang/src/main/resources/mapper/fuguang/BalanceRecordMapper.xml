<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.BalanceRecordMapper">
    
    <resultMap type="BalanceRecord" id="BalanceRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="changeType"    column="change_type"    />
        <result property="incomeType"    column="income_type"    />
        <result property="changeAmount"    column="change_amount"    />
        <result property="balanceBefore"    column="balance_before"    />
        <result property="balanceAfter"    column="balance_after"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="businessNo"    column="business_no"    />
        <result property="description"    column="description"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBalanceRecordVo">
        select record_id, user_id, user_name, change_type, income_type, change_amount, balance_before, balance_after, business_type, business_id, business_no, description, create_time from balance_record
    </sql>

    <select id="selectBalanceRecordList" parameterType="BalanceRecord" resultMap="BalanceRecordResult">
        <include refid="selectBalanceRecordVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="changeType != null  and changeType != ''"> and change_type = #{changeType}</if>
            <if test="incomeType != null  and incomeType != ''"> and income_type = #{incomeType}</if>
            <if test="changeAmount != null "> and change_amount = #{changeAmount}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="businessNo != null  and businessNo != ''"> and business_no = #{businessNo}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectBalanceRecordByRecordId" parameterType="Long" resultMap="BalanceRecordResult">
        <include refid="selectBalanceRecordVo"/>
        where record_id = #{recordId}
    </select>
    
    <select id="selectBalanceRecordListByUserId" parameterType="Long" resultMap="BalanceRecordResult">
        <include refid="selectBalanceRecordVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>
    
    <select id="selectBalanceRecordListByUserIdAndTime" resultMap="BalanceRecordResult">
        <include refid="selectBalanceRecordVo"/>
        where user_id = #{userId}
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and create_time &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>
        
    <insert id="insertBalanceRecord" parameterType="BalanceRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into balance_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="changeType != null and changeType != ''">change_type,</if>
            <if test="incomeType != null and incomeType != ''">income_type,</if>
            <if test="changeAmount != null">change_amount,</if>
            <if test="balanceBefore != null">balance_before,</if>
            <if test="balanceAfter != null">balance_after,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="businessNo != null and businessNo != ''">business_no,</if>
            <if test="description != null and description != ''">description,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="changeType != null and changeType != ''">#{changeType},</if>
            <if test="incomeType != null and incomeType != ''">#{incomeType},</if>
            <if test="changeAmount != null">#{changeAmount},</if>
            <if test="balanceBefore != null">#{balanceBefore},</if>
            <if test="balanceAfter != null">#{balanceAfter},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="businessNo != null and businessNo != ''">#{businessNo},</if>
            <if test="description != null and description != ''">#{description},</if>
            now()
         </trim>
    </insert>

    <update id="updateBalanceRecord" parameterType="BalanceRecord">
        update balance_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="changeType != null and changeType != ''">change_type = #{changeType},</if>
            <if test="incomeType != null and incomeType != ''">income_type = #{incomeType},</if>
            <if test="changeAmount != null">change_amount = #{changeAmount},</if>
            <if test="balanceBefore != null">balance_before = #{balanceBefore},</if>
            <if test="balanceAfter != null">balance_after = #{balanceAfter},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessNo != null and businessNo != ''">business_no = #{businessNo},</if>
            <if test="description != null and description != ''">description = #{description},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteBalanceRecordByRecordId" parameterType="Long">
        delete from balance_record where record_id = #{recordId}
    </delete>

    <delete id="deleteBalanceRecordByRecordIds" parameterType="String">
        delete from balance_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

</mapper>
