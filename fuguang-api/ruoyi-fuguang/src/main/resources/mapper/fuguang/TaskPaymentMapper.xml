<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.TaskPaymentMapper">
    
    <resultMap type="TaskPayment" id="TaskPaymentResult">
        <id     property="paymentId"    column="payment_id"    />
        <result property="taskId"       column="task_id"       />
        <result property="taskTitle"    column="task_title"    />
        <result property="userId"       column="user_id"       />
        <result property="userName"     column="user_name"     />
        <result property="orderNo"      column="order_no"      />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payType"      column="pay_type"      />
        <result property="payStatus"    column="pay_status"    />
        <result property="tradeNo"      column="trade_no"      />
        <result property="payTime"      column="pay_time"      />
        <result property="notifyTime"   column="notify_time"   />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <sql id="selectTaskPaymentVo">
        select payment_id, task_id, task_title, user_id, user_name, order_no, pay_amount, pay_type, pay_status, trade_no, pay_time, notify_time, create_time, update_time from task_payment
    </sql>

    <select id="selectTaskPaymentList" parameterType="TaskPayment" resultMap="TaskPaymentResult">
        <include refid="selectTaskPaymentVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskTitle != null  and taskTitle != ''"> and task_title like concat('%', #{taskTitle}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="payAmount != null "> and pay_amount = #{payAmount}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="notifyTime != null "> and notify_time = #{notifyTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTaskPaymentByPaymentId" parameterType="Long" resultMap="TaskPaymentResult">
        <include refid="selectTaskPaymentVo"/>
        where payment_id = #{paymentId}
    </select>

    <select id="selectTaskPaymentByTaskId" parameterType="Long" resultMap="TaskPaymentResult">
        <include refid="selectTaskPaymentVo"/>
        where task_id = #{taskId}
    </select>

    <select id="selectTaskPaymentByOrderNo" parameterType="String" resultMap="TaskPaymentResult">
        <include refid="selectTaskPaymentVo"/>
        where order_no = #{orderNo}
    </select>

    <select id="selectTaskPaymentListByUserId" parameterType="Long" resultMap="TaskPaymentResult">
        <include refid="selectTaskPaymentVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>
        
    <insert id="insertTaskPayment" parameterType="TaskPayment" useGeneratedKeys="true" keyProperty="paymentId">
        insert into task_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="taskTitle != null and taskTitle != ''">task_title,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="notifyTime != null">notify_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="taskTitle != null and taskTitle != ''">#{taskTitle},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="notifyTime != null">#{notifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTaskPayment" parameterType="TaskPayment">
        update task_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskTitle != null and taskTitle != ''">task_title = #{taskTitle},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="notifyTime != null">notify_time = #{notifyTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where payment_id = #{paymentId}
    </update>

    <update id="updatePaymentStatusByOrderNo">
        update task_payment 
        set pay_status = #{payStatus}, 
            trade_no = #{tradeNo}, 
            pay_time = now(), 
            notify_time = now(),
            update_time = now()
        where order_no = #{orderNo}
    </update>

    <delete id="deleteTaskPaymentByPaymentId" parameterType="Long">
        delete from task_payment where payment_id = #{paymentId}
    </delete>

    <delete id="deleteTaskPaymentByPaymentIds" parameterType="String">
        delete from task_payment where payment_id in
        <foreach item="paymentId" collection="array" open="(" separator="," close=")">
            #{paymentId}
        </foreach>
    </delete>
</mapper>
