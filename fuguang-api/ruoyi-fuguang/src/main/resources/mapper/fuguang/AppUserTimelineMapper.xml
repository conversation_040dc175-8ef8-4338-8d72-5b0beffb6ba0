<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppUserTimelineMapper">
    
    <resultMap type="AppUserTimeline" id="AppUserTimelineResult">
        <result property="timelineId"    column="timeline_id"    />
        <result property="userId"    column="user_id"    />
        <result property="eventType"    column="event_type"    />
        <result property="eventTitle"    column="event_title"    />
        <result property="eventDesc"    column="event_desc"    />
        <result property="eventData"    column="event_data"    />
        <result property="eventTime"    column="event_time"    />
        <result property="icon"    column="icon"    />
        <result property="color"    column="color"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppUserTimelineVo">
        select timeline_id, user_id, event_type, event_title, event_desc, event_data, event_time, icon, color, status, create_by, create_time, update_by, update_time, remark from app_user_timeline
    </sql>

    <select id="selectAppUserTimelineList" parameterType="AppUserTimeline" resultMap="AppUserTimelineResult">
        <include refid="selectAppUserTimelineVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="eventTitle != null  and eventTitle != ''"> and event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="eventDesc != null  and eventDesc != ''"> and event_desc like concat('%', #{eventDesc}, '%')</if>
            <if test="eventTime != null "> and event_time = #{eventTime}</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
            <if test="color != null  and color != ''"> and color = #{color}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by event_time desc
    </select>
    
    <select id="selectAppUserTimelineByTimelineId" parameterType="Long" resultMap="AppUserTimelineResult">
        <include refid="selectAppUserTimelineVo"/>
        where timeline_id = #{timelineId}
    </select>

    <select id="selectAppUserTimelineByUserId" parameterType="Long" resultMap="AppUserTimelineResult">
        <include refid="selectAppUserTimelineVo"/>
        where user_id = #{userId} and status = '0'
        order by event_time desc
    </select>
        
    <insert id="insertAppUserTimeline" parameterType="AppUserTimeline" useGeneratedKeys="true" keyProperty="timelineId">
        insert into app_user_timeline
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="eventType != null">event_type,</if>
            <if test="eventTitle != null">event_title,</if>
            <if test="eventDesc != null">event_desc,</if>
            <if test="eventData != null">event_data,</if>
            <if test="eventTime != null">event_time,</if>
            <if test="icon != null">icon,</if>
            <if test="color != null">color,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="eventTitle != null">#{eventTitle},</if>
            <if test="eventDesc != null">#{eventDesc},</if>
            <if test="eventData != null">#{eventData},</if>
            <if test="eventTime != null">#{eventTime},</if>
            <if test="icon != null">#{icon},</if>
            <if test="color != null">#{color},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppUserTimeline" parameterType="AppUserTimeline">
        update app_user_timeline
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="eventTitle != null">event_title = #{eventTitle},</if>
            <if test="eventDesc != null">event_desc = #{eventDesc},</if>
            <if test="eventData != null">event_data = #{eventData},</if>
            <if test="eventTime != null">event_time = #{eventTime},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="color != null">color = #{color},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where timeline_id = #{timelineId}
    </update>

    <delete id="deleteAppUserTimelineByTimelineId" parameterType="Long">
        delete from app_user_timeline where timeline_id = #{timelineId}
    </delete>

    <delete id="deleteAppUserTimelineByTimelineIds" parameterType="String">
        delete from app_user_timeline where timeline_id in 
        <foreach item="timelineId" collection="array" open="(" separator="," close=")">
            #{timelineId}
        </foreach>
    </delete>

</mapper>
