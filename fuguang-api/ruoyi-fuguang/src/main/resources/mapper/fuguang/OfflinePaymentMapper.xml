<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.OfflinePaymentMapper">
    
    <resultMap type="OfflinePayment" id="OfflinePaymentResult">
        <result property="paymentId"    column="payment_id"    />
        <result property="merchantId"    column="merchant_id"    />
        <result property="merchantName"    column="merchant_name"    />
        <result property="orderNo"    column="order_no"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payType"    column="pay_type"    />
        <result property="payStatus"    column="pay_status"    />
        <result property="tradeNo"    column="trade_no"    />
        <result property="payTime"    column="pay_time"    />
        <result property="notifyTime"    column="notify_time"    />
        <result property="transferStatus"    column="transfer_status"    />
        <result property="transferNo"    column="transfer_no"    />
        <result property="transferTime"    column="transfer_time"    />
        <result property="transferAmount"    column="transfer_amount"    />
        <result property="platformFee"    column="platform_fee"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOfflinePaymentVo">
        select payment_id, merchant_id, merchant_name, order_no, pay_amount, pay_type, pay_status, trade_no, pay_time, notify_time, transfer_status, transfer_no, transfer_time, transfer_amount, platform_fee, create_time, update_time, remark from offline_payment
    </sql>

    <select id="selectOfflinePaymentList" parameterType="OfflinePayment" resultMap="OfflinePaymentResult">
        <include refid="selectOfflinePaymentVo"/>
        <where>  
            <if test="merchantId != null "> and merchant_id = #{merchantId}</if>
            <if test="merchantName != null  and merchantName != ''"> and merchant_name like concat('%', #{merchantName}, '%')</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="payAmount != null "> and pay_amount = #{payAmount}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="notifyTime != null "> and notify_time = #{notifyTime}</if>
            <if test="transferStatus != null  and transferStatus != ''"> and transfer_status = #{transferStatus}</if>
            <if test="transferNo != null  and transferNo != ''"> and transfer_no = #{transferNo}</if>
            <if test="transferTime != null "> and transfer_time = #{transferTime}</if>
            <if test="transferAmount != null "> and transfer_amount = #{transferAmount}</if>
            <if test="platformFee != null "> and platform_fee = #{platformFee}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOfflinePaymentByPaymentId" parameterType="Long" resultMap="OfflinePaymentResult">
        <include refid="selectOfflinePaymentVo"/>
        where payment_id = #{paymentId}
    </select>

    <select id="selectOfflinePaymentByOrderNo" parameterType="String" resultMap="OfflinePaymentResult">
        <include refid="selectOfflinePaymentVo"/>
        where order_no = #{orderNo}
    </select>
        
    <insert id="insertOfflinePayment" parameterType="OfflinePayment" useGeneratedKeys="true" keyProperty="paymentId">
        insert into offline_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">merchant_id,</if>
            <if test="merchantName != null and merchantName != ''">merchant_name,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="notifyTime != null">notify_time,</if>
            <if test="transferStatus != null">transfer_status,</if>
            <if test="transferNo != null">transfer_no,</if>
            <if test="transferTime != null">transfer_time,</if>
            <if test="transferAmount != null">transfer_amount,</if>
            <if test="platformFee != null">platform_fee,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">#{merchantId},</if>
            <if test="merchantName != null and merchantName != ''">#{merchantName},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="notifyTime != null">#{notifyTime},</if>
            <if test="transferStatus != null">#{transferStatus},</if>
            <if test="transferNo != null">#{transferNo},</if>
            <if test="transferTime != null">#{transferTime},</if>
            <if test="transferAmount != null">#{transferAmount},</if>
            <if test="platformFee != null">#{platformFee},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOfflinePayment" parameterType="OfflinePayment">
        update offline_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="merchantName != null and merchantName != ''">merchant_name = #{merchantName},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="notifyTime != null">notify_time = #{notifyTime},</if>
            <if test="transferStatus != null">transfer_status = #{transferStatus},</if>
            <if test="transferNo != null">transfer_no = #{transferNo},</if>
            <if test="transferTime != null">transfer_time = #{transferTime},</if>
            <if test="transferAmount != null">transfer_amount = #{transferAmount},</if>
            <if test="platformFee != null">platform_fee = #{platformFee},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where payment_id = #{paymentId}
    </update>

    <delete id="deleteOfflinePaymentByPaymentId" parameterType="Long">
        delete from offline_payment where payment_id = #{paymentId}
    </delete>

    <delete id="deleteOfflinePaymentByPaymentIds" parameterType="String">
        delete from offline_payment where payment_id in
        <foreach item="paymentId" collection="array" open="(" separator="," close=")">
            #{paymentId}
        </foreach>
    </delete>

</mapper>
