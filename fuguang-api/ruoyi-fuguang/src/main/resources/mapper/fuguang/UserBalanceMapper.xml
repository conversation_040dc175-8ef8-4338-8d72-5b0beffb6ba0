<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.UserBalanceMapper">
    
    <resultMap type="UserBalance" id="UserBalanceResult">
        <result property="balanceId"    column="balance_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="totalBalance"    column="total_balance"    />
        <result property="availableBalance"    column="available_balance"    />
        <result property="frozenBalance"    column="frozen_balance"    />
        <result property="totalIncome"    column="total_income"    />
        <result property="totalWithdraw"    column="total_withdraw"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserBalanceVo">
        select balance_id, user_id, user_name, total_balance, available_balance, frozen_balance, total_income, total_withdraw, create_time, update_time from user_balance
    </sql>

    <select id="selectUserBalanceList" parameterType="UserBalance" resultMap="UserBalanceResult">
        <include refid="selectUserBalanceVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="totalBalance != null "> and total_balance = #{totalBalance}</if>
            <if test="availableBalance != null "> and available_balance = #{availableBalance}</if>
            <if test="frozenBalance != null "> and frozen_balance = #{frozenBalance}</if>
            <if test="totalIncome != null "> and total_income = #{totalIncome}</if>
            <if test="totalWithdraw != null "> and total_withdraw = #{totalWithdraw}</if>
        </where>
    </select>
    
    <select id="selectUserBalanceByBalanceId" parameterType="Long" resultMap="UserBalanceResult">
        <include refid="selectUserBalanceVo"/>
        where balance_id = #{balanceId}
    </select>
    
    <select id="selectUserBalanceByUserId" parameterType="Long" resultMap="UserBalanceResult">
        <include refid="selectUserBalanceVo"/>
        where user_id = #{userId}
    </select>
        
    <insert id="insertUserBalance" parameterType="UserBalance" useGeneratedKeys="true" keyProperty="balanceId">
        insert into user_balance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="totalBalance != null">total_balance,</if>
            <if test="availableBalance != null">available_balance,</if>
            <if test="frozenBalance != null">frozen_balance,</if>
            <if test="totalIncome != null">total_income,</if>
            <if test="totalWithdraw != null">total_withdraw,</if>
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="totalBalance != null">#{totalBalance},</if>
            <if test="availableBalance != null">#{availableBalance},</if>
            <if test="frozenBalance != null">#{frozenBalance},</if>
            <if test="totalIncome != null">#{totalIncome},</if>
            <if test="totalWithdraw != null">#{totalWithdraw},</if>
            now(),
            now()
         </trim>
    </insert>

    <update id="updateUserBalance" parameterType="UserBalance">
        update user_balance
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="totalBalance != null">total_balance = #{totalBalance},</if>
            <if test="availableBalance != null">available_balance = #{availableBalance},</if>
            <if test="frozenBalance != null">frozen_balance = #{frozenBalance},</if>
            <if test="totalIncome != null">total_income = #{totalIncome},</if>
            <if test="totalWithdraw != null">total_withdraw = #{totalWithdraw},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where balance_id = #{balanceId}
    </update>

    <delete id="deleteUserBalanceByBalanceId" parameterType="Long">
        delete from user_balance where balance_id = #{balanceId}
    </delete>

    <delete id="deleteUserBalanceByBalanceIds" parameterType="String">
        delete from user_balance where balance_id in 
        <foreach item="balanceId" collection="array" open="(" separator="," close=")">
            #{balanceId}
        </foreach>
    </delete>
    
    <update id="increaseBalance">
        update user_balance set 
            total_balance = total_balance + #{amount},
            available_balance = available_balance + #{amount},
            update_time = now()
        where user_id = #{userId}
    </update>
    
    <update id="decreaseBalance">
        update user_balance set 
            total_balance = total_balance - #{amount},
            available_balance = available_balance - #{amount},
            update_time = now()
        where user_id = #{userId} and available_balance >= #{amount}
    </update>
    
    <update id="freezeBalance">
        update user_balance set 
            available_balance = available_balance - #{amount},
            frozen_balance = frozen_balance + #{amount},
            update_time = now()
        where user_id = #{userId} and available_balance >= #{amount}
    </update>
    
    <update id="unfreezeBalance">
        update user_balance set 
            available_balance = available_balance + #{amount},
            frozen_balance = frozen_balance - #{amount},
            update_time = now()
        where user_id = #{userId} and frozen_balance >= #{amount}
    </update>
    
    <update id="updateTotalIncome">
        update user_balance set 
            total_income = total_income + #{amount},
            update_time = now()
        where user_id = #{userId}
    </update>
    
    <update id="updateTotalWithdraw">
        update user_balance set 
            total_withdraw = total_withdraw + #{amount},
            update_time = now()
        where user_id = #{userId}
    </update>

</mapper>
