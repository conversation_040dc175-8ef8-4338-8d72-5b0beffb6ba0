<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppTaskTypeMapper">
    
    <resultMap type="AppTaskType" id="AppTaskTypeResult">
        <result property="typeId"    column="type_id"    />
        <result property="typeName"    column="type_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="typeIcon"    column="type_icon"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="typeDesc"    column="type_desc"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppTaskTypeVo">
        select type_id, type_name, parent_id, type_icon, order_num, status, type_desc, create_by, create_time, update_by, update_time, remark from app_task_type
    </sql>

    <select id="selectAppTaskTypeList" parameterType="AppTaskType" resultMap="AppTaskTypeResult">
        <include refid="selectAppTaskTypeVo"/>
        <where>  
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by order_num asc, create_time desc
    </select>
    
    <select id="selectAppTaskTypeByTypeId" parameterType="Long" resultMap="AppTaskTypeResult">
        <include refid="selectAppTaskTypeVo"/>
        where type_id = #{typeId}
    </select>

    <select id="selectFirstLevelTaskTypes" resultMap="AppTaskTypeResult">
        <include refid="selectAppTaskTypeVo"/>
        where parent_id = 0 and status = '0'
        order by order_num asc, create_time desc
    </select>

    <select id="selectTaskTypesByParentId" parameterType="Long" resultMap="AppTaskTypeResult">
        <include refid="selectAppTaskTypeVo"/>
        where parent_id = #{parentId} and status = '0'
        order by order_num asc, create_time desc
    </select>
        
    <insert id="insertAppTaskType" parameterType="AppTaskType" useGeneratedKeys="true" keyProperty="typeId">
        insert into app_task_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="typeIcon != null">type_icon,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="typeDesc != null">type_desc,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="typeIcon != null">#{typeIcon},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="typeDesc != null">#{typeDesc},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppTaskType" parameterType="AppTaskType">
        update app_task_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="typeIcon != null">type_icon = #{typeIcon},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="typeDesc != null">type_desc = #{typeDesc},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where type_id = #{typeId}
    </update>

    <delete id="deleteAppTaskTypeByTypeId" parameterType="Long">
        delete from app_task_type where type_id = #{typeId}
    </delete>

    <delete id="deleteAppTaskTypeByTypeIds" parameterType="String">
        delete from app_task_type where type_id in 
        <foreach item="typeId" collection="array" open="(" separator="," close=")">
            #{typeId}
        </foreach>
    </delete>
</mapper>
