<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallCategoryMapper">

    <resultMap type="MallCategory" id="MallCategoryResult">
        <id     property="categoryId"    column="category_id"    />
        <result property="parentId"      column="parent_id"      />
        <result property="categoryName"  column="category_name"  />
        <result property="categoryIcon"  column="category_icon"  />
        <result property="sortOrder"     column="sort_order"     />
        <result property="status"        column="status"         />
        <result property="delFlag"       column="del_flag"       />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
    </resultMap>

    <sql id="selectMallCategoryVo">
        select category_id, parent_id, category_name, category_icon, sort_order, status, del_flag, create_by, create_time, update_by, update_time, remark from mall_category
    </sql>

    <select id="selectMallCategoryList" parameterType="MallCategory" resultMap="MallCategoryResult">
        <include refid="selectMallCategoryVo"/>
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by parent_id, sort_order
    </select>

    <select id="selectMallCategoryByCategoryId" parameterType="Long" resultMap="MallCategoryResult">
        <include refid="selectMallCategoryVo"/>
        where category_id = #{categoryId} and del_flag = '0'
    </select>

    <select id="selectChildrenCategoryCount" parameterType="Long" resultType="int">
        select count(*) from mall_category where parent_id = #{parentId} and del_flag = '0'
    </select>

    <select id="selectEnabledCategoryTree" resultMap="MallCategoryResult">
        <include refid="selectMallCategoryVo"/>
        where status = '0' and del_flag = '0'
        order by parent_id, sort_order
    </select>

    <select id="selectTopCategoryList" resultMap="MallCategoryResult">
        <include refid="selectMallCategoryVo"/>
        where parent_id = 0 and status = '0' and del_flag = '0'
        order by sort_order
    </select>

    <insert id="insertMallCategory" parameterType="MallCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into mall_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryIcon != null">category_icon,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryIcon != null">#{categoryIcon},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMallCategory" parameterType="MallCategory">
        update mall_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryIcon != null">category_icon = #{categoryIcon},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteMallCategoryByCategoryId" parameterType="Long">
        update mall_category set del_flag = '2' where category_id = #{categoryId}
    </delete>

    <delete id="deleteMallCategoryByCategoryIds" parameterType="String">
        update mall_category set del_flag = '2' where category_id in
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

</mapper>