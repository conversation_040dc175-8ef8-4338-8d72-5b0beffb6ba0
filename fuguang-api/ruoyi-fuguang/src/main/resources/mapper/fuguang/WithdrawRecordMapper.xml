<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.WithdrawRecordMapper">
    
    <resultMap type="WithdrawRecord" id="WithdrawRecordResult">
        <id     property="withdrawId"       column="withdraw_id"       />
        <result property="userId"           column="user_id"           />
        <result property="userName"         column="user_name"         />
        <result property="withdrawNo"       column="withdraw_no"       />
        <result property="withdrawAmount"   column="withdraw_amount"   />
        <result property="withdrawFee"      column="withdraw_fee"      />
        <result property="actualAmount"     column="actual_amount"     />
        <result property="withdrawType"     column="withdraw_type"     />
        <result property="withdrawStatus"   column="withdraw_status"   />
        <result property="payeeAccount"     column="payee_account"     />
        <result property="payeeName"        column="payee_name"        />
        <result property="tradeNo"          column="trade_no"          />
        <result property="failReason"       column="fail_reason"       />
        <result property="applyTime"        column="apply_time"        />
        <result property="processTime"      column="process_time"      />
        <result property="finishTime"       column="finish_time"       />
        <result property="createTime"       column="create_time"       />
        <result property="updateTime"       column="update_time"       />
    </resultMap>

    <sql id="selectWithdrawRecordVo">
        select withdraw_id, user_id, user_name, withdraw_no, withdraw_amount, withdraw_fee, actual_amount, withdraw_type, withdraw_status, payee_account, payee_name, trade_no, fail_reason, apply_time, process_time, finish_time, create_time, update_time from withdraw_record
    </sql>

    <select id="selectWithdrawRecordList" parameterType="WithdrawRecord" resultMap="WithdrawRecordResult">
        <include refid="selectWithdrawRecordVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="withdrawNo != null  and withdrawNo != ''"> and withdraw_no = #{withdrawNo}</if>
            <if test="withdrawAmount != null "> and withdraw_amount = #{withdrawAmount}</if>
            <if test="withdrawType != null  and withdrawType != ''"> and withdraw_type = #{withdrawType}</if>
            <if test="withdrawStatus != null  and withdrawStatus != ''"> and withdraw_status = #{withdrawStatus}</if>
            <if test="payeeAccount != null  and payeeAccount != ''"> and payee_account like concat('%', #{payeeAccount}, '%')</if>
            <if test="payeeName != null  and payeeName != ''"> and payee_name like concat('%', #{payeeName}, '%')</if>
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
        </where>
        order by apply_time desc
    </select>
    
    <select id="selectWithdrawRecordByWithdrawId" parameterType="Long" resultMap="WithdrawRecordResult">
        <include refid="selectWithdrawRecordVo"/>
        where withdraw_id = #{withdrawId}
    </select>

    <select id="selectWithdrawRecordByWithdrawNo" parameterType="String" resultMap="WithdrawRecordResult">
        <include refid="selectWithdrawRecordVo"/>
        where withdraw_no = #{withdrawNo}
    </select>

    <select id="selectWithdrawRecordsByUserId" parameterType="Long" resultMap="WithdrawRecordResult">
        <include refid="selectWithdrawRecordVo"/>
        where user_id = #{userId}
        order by apply_time desc
    </select>
        
    <insert id="insertWithdrawRecord" parameterType="WithdrawRecord" useGeneratedKeys="true" keyProperty="withdrawId">
        insert into withdraw_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="withdrawNo != null and withdrawNo != ''">withdraw_no,</if>
            <if test="withdrawAmount != null">withdraw_amount,</if>
            <if test="withdrawFee != null">withdraw_fee,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="withdrawType != null">withdraw_type,</if>
            <if test="withdrawStatus != null">withdraw_status,</if>
            <if test="payeeAccount != null and payeeAccount != ''">payee_account,</if>
            <if test="payeeName != null and payeeName != ''">payee_name,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="processTime != null">process_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="withdrawNo != null and withdrawNo != ''">#{withdrawNo},</if>
            <if test="withdrawAmount != null">#{withdrawAmount},</if>
            <if test="withdrawFee != null">#{withdrawFee},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="withdrawType != null">#{withdrawType},</if>
            <if test="withdrawStatus != null">#{withdrawStatus},</if>
            <if test="payeeAccount != null and payeeAccount != ''">#{payeeAccount},</if>
            <if test="payeeName != null and payeeName != ''">#{payeeName},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWithdrawRecord" parameterType="WithdrawRecord">
        update withdraw_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="withdrawNo != null and withdrawNo != ''">withdraw_no = #{withdrawNo},</if>
            <if test="withdrawAmount != null">withdraw_amount = #{withdrawAmount},</if>
            <if test="withdrawFee != null">withdraw_fee = #{withdrawFee},</if>
            <if test="actualAmount != null">actual_amount = #{actualAmount},</if>
            <if test="withdrawType != null">withdraw_type = #{withdrawType},</if>
            <if test="withdrawStatus != null">withdraw_status = #{withdrawStatus},</if>
            <if test="payeeAccount != null and payeeAccount != ''">payee_account = #{payeeAccount},</if>
            <if test="payeeName != null and payeeName != ''">payee_name = #{payeeName},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="failReason != null">fail_reason = #{failReason},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where withdraw_id = #{withdrawId}
    </update>

    <delete id="deleteWithdrawRecordByWithdrawId" parameterType="Long">
        delete from withdraw_record where withdraw_id = #{withdrawId}
    </delete>

    <delete id="deleteWithdrawRecordByWithdrawIds" parameterType="String">
        delete from withdraw_record where withdraw_id in 
        <foreach item="withdrawId" collection="array" open="(" separator="," close=")">
            #{withdrawId}
        </foreach>
    </delete>
</mapper>
