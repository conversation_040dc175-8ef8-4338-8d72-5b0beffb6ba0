<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallPaymentMapper">

    <resultMap type="MallPayment" id="MallPaymentResult">
        <id     property="paymentId"    column="payment_id"    />
        <result property="orderId"      column="order_id"      />
        <result property="orderNo"      column="order_no"      />
        <result property="userId"       column="user_id"       />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payType"      column="pay_type"      />
        <result property="payStatus"    column="pay_status"    />
        <result property="tradeNo"      column="trade_no"      />
        <result property="payTime"      column="pay_time"      />
        <result property="notifyTime"   column="notify_time"   />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <sql id="selectMallPaymentVo">
        select payment_id, order_id, order_no, user_id, pay_amount, pay_type, pay_status, trade_no, pay_time, notify_time, create_time, update_time from mall_payment
    </sql>

    <select id="selectMallPaymentList" parameterType="MallPayment" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectMallPaymentByPaymentId" parameterType="Long" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where payment_id = #{paymentId}
    </select>

    <select id="selectPaymentByOrderId" parameterType="Long" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectPaymentByOrderNo" parameterType="String" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where order_no = #{orderNo}
    </select>

    <select id="selectPaymentByTradeNo" parameterType="String" resultMap="MallPaymentResult">
        <include refid="selectMallPaymentVo"/>
        where trade_no = #{tradeNo}
    </select>

    <insert id="insertMallPayment" parameterType="MallPayment" useGeneratedKeys="true" keyProperty="paymentId">
        insert into mall_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="notifyTime != null">notify_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="notifyTime != null">#{notifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMallPayment" parameterType="MallPayment">
        update mall_payment
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="notifyTime != null">notify_time = #{notifyTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where payment_id = #{paymentId}
    </update>

    <update id="updatePaymentStatus">
        update mall_payment
        <trim prefix="SET" suffixOverrides=",">
            pay_status = #{payStatus},
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payStatus == '1'">pay_time = now(),</if>
            notify_time = now(),
            update_time = now(),
        </trim>
        where payment_id = #{paymentId}
    </update>

    <update id="updatePaymentStatusByOrderNo">
        update mall_payment
        <trim prefix="SET" suffixOverrides=",">
            pay_status = #{payStatus},
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="payStatus == '1'">pay_time = now(),</if>
            notify_time = now(),
            update_time = now(),
        </trim>
        where order_no = #{orderNo}
    </update>

    <delete id="deleteMallPaymentByPaymentId" parameterType="Long">
        delete from mall_payment where payment_id = #{paymentId}
    </delete>

    <delete id="deleteMallPaymentByPaymentIds" parameterType="String">
        delete from mall_payment where payment_id in
        <foreach item="paymentId" collection="array" open="(" separator="," close=")">
            #{paymentId}
        </foreach>
    </delete>

</mapper>