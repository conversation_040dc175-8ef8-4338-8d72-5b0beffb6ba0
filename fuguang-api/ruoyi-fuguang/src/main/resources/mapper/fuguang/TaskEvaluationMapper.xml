<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.TaskEvaluationMapper">
    
    <resultMap type="TaskEvaluation" id="TaskEvaluationResult">
        <result property="evaluationId"    column="evaluation_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskTitle"    column="task_title"    />
        <result property="publisherId"    column="publisher_id"    />
        <result property="publisherName"    column="publisher_name"    />
        <result property="receiverId"    column="receiver_id"    />
        <result property="receiverName"    column="receiver_name"    />
        <result property="rating"    column="rating"    />
        <result property="evaluationContent"    column="evaluation_content"    />
        <result property="evaluationTags"    column="evaluation_tags"    />
        <result property="isAnonymous"    column="is_anonymous"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTaskEvaluationVo">
        select evaluation_id, task_id, task_title, publisher_id, publisher_name, receiver_id, receiver_name, rating, evaluation_content, evaluation_tags, is_anonymous, status, create_by, create_time, update_by, update_time, remark from task_evaluation
    </sql>

    <select id="selectTaskEvaluationList" parameterType="TaskEvaluation" resultMap="TaskEvaluationResult">
        <include refid="selectTaskEvaluationVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskTitle != null  and taskTitle != ''"> and task_title like concat('%', #{taskTitle}, '%')</if>
            <if test="publisherId != null "> and publisher_id = #{publisherId}</if>
            <if test="publisherName != null  and publisherName != ''"> and publisher_name like concat('%', #{publisherName}, '%')</if>
            <if test="receiverId != null "> and receiver_id = #{receiverId}</if>
            <if test="receiverName != null  and receiverName != ''"> and receiver_name like concat('%', #{receiverName}, '%')</if>
            <if test="rating != null "> and rating = #{rating}</if>
            <if test="evaluationContent != null  and evaluationContent != ''"> and evaluation_content like concat('%', #{evaluationContent}, '%')</if>
            <if test="isAnonymous != null  and isAnonymous != ''"> and is_anonymous = #{isAnonymous}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTaskEvaluationByEvaluationId" parameterType="Long" resultMap="TaskEvaluationResult">
        <include refid="selectTaskEvaluationVo"/>
        where evaluation_id = #{evaluationId}
    </select>

    <select id="selectTaskEvaluationByTaskId" parameterType="Long" resultMap="TaskEvaluationResult">
        <include refid="selectTaskEvaluationVo"/>
        where task_id = #{taskId}
    </select>

    <select id="selectTaskEvaluationByReceiverId" parameterType="Long" resultMap="TaskEvaluationResult">
        <include refid="selectTaskEvaluationVo"/>
        where receiver_id = #{receiverId} and status = '0'
        order by create_time desc
    </select>

    <select id="selectTaskEvaluationByPublisherId" parameterType="Long" resultMap="TaskEvaluationResult">
        <include refid="selectTaskEvaluationVo"/>
        where publisher_id = #{publisherId} and status = '0'
        order by create_time desc
    </select>

    <select id="selectAverageRatingByReceiverId" parameterType="Long" resultType="Double">
        select avg(rating) from task_evaluation 
        where receiver_id = #{receiverId} and status = '0'
    </select>

    <select id="selectEvaluationCountByReceiverId" parameterType="Long" resultType="Integer">
        select count(*) from task_evaluation 
        where receiver_id = #{receiverId} and status = '0'
    </select>

    <select id="selectRatingStatsByReceiverId" parameterType="Long" resultType="java.util.Map">
        select rating, count(*) as count from task_evaluation 
        where receiver_id = #{receiverId} and status = '0'
        group by rating
        order by rating desc
    </select>
        
    <insert id="insertTaskEvaluation" parameterType="TaskEvaluation" useGeneratedKeys="true" keyProperty="evaluationId">
        insert into task_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="taskTitle != null and taskTitle != ''">task_title,</if>
            <if test="publisherId != null">publisher_id,</if>
            <if test="publisherName != null and publisherName != ''">publisher_name,</if>
            <if test="receiverId != null">receiver_id,</if>
            <if test="receiverName != null and receiverName != ''">receiver_name,</if>
            <if test="rating != null">rating,</if>
            <if test="evaluationContent != null">evaluation_content,</if>
            <if test="evaluationTags != null">evaluation_tags,</if>
            <if test="isAnonymous != null">is_anonymous,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="taskTitle != null and taskTitle != ''">#{taskTitle},</if>
            <if test="publisherId != null">#{publisherId},</if>
            <if test="publisherName != null and publisherName != ''">#{publisherName},</if>
            <if test="receiverId != null">#{receiverId},</if>
            <if test="receiverName != null and receiverName != ''">#{receiverName},</if>
            <if test="rating != null">#{rating},</if>
            <if test="evaluationContent != null">#{evaluationContent},</if>
            <if test="evaluationTags != null">#{evaluationTags},</if>
            <if test="isAnonymous != null">#{isAnonymous},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTaskEvaluation" parameterType="TaskEvaluation">
        update task_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskTitle != null and taskTitle != ''">task_title = #{taskTitle},</if>
            <if test="publisherId != null">publisher_id = #{publisherId},</if>
            <if test="publisherName != null and publisherName != ''">publisher_name = #{publisherName},</if>
            <if test="receiverId != null">receiver_id = #{receiverId},</if>
            <if test="receiverName != null and receiverName != ''">receiver_name = #{receiverName},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="evaluationContent != null">evaluation_content = #{evaluationContent},</if>
            <if test="evaluationTags != null">evaluation_tags = #{evaluationTags},</if>
            <if test="isAnonymous != null">is_anonymous = #{isAnonymous},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where evaluation_id = #{evaluationId}
    </update>

    <delete id="deleteTaskEvaluationByEvaluationId" parameterType="Long">
        delete from task_evaluation where evaluation_id = #{evaluationId}
    </delete>

    <delete id="deleteTaskEvaluationByEvaluationIds" parameterType="String">
        delete from task_evaluation where evaluation_id in 
        <foreach item="evaluationId" collection="array" open="(" separator="," close=")">
            #{evaluationId}
        </foreach>
    </delete>
</mapper>
