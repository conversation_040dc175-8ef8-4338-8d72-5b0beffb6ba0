<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MerchantQrcodeMapper">
    
    <resultMap type="MerchantQrcode" id="MerchantQrcodeResult">
        <result property="qrcodeId"    column="qrcode_id"    />
        <result property="merchantId"    column="merchant_id"    />
        <result property="merchantName"    column="merchant_name"    />
        <result property="qrcodeContent"    column="qrcode_content"    />
        <result property="qrcodeUrl"    column="qrcode_url"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMerchantQrcodeVo">
        select qrcode_id, merchant_id, merchant_name, qrcode_content, qrcode_url, status, create_time, update_time from merchant_qrcode
    </sql>

    <select id="selectMerchantQrcodeList" parameterType="MerchantQrcode" resultMap="MerchantQrcodeResult">
        <include refid="selectMerchantQrcodeVo"/>
        <where>  
            <if test="merchantId != null "> and merchant_id = #{merchantId}</if>
            <if test="merchantName != null  and merchantName != ''"> and merchant_name like concat('%', #{merchantName}, '%')</if>
            <if test="qrcodeContent != null  and qrcodeContent != ''"> and qrcode_content = #{qrcodeContent}</if>
            <if test="qrcodeUrl != null  and qrcodeUrl != ''"> and qrcode_url = #{qrcodeUrl}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMerchantQrcodeByQrcodeId" parameterType="Long" resultMap="MerchantQrcodeResult">
        <include refid="selectMerchantQrcodeVo"/>
        where qrcode_id = #{qrcodeId}
    </select>

    <select id="selectMerchantQrcodeByMerchantId" parameterType="Long" resultMap="MerchantQrcodeResult">
        <include refid="selectMerchantQrcodeVo"/>
        where merchant_id = #{merchantId}
    </select>
        
    <insert id="insertMerchantQrcode" parameterType="MerchantQrcode" useGeneratedKeys="true" keyProperty="qrcodeId">
        insert into merchant_qrcode
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">merchant_id,</if>
            <if test="merchantName != null and merchantName != ''">merchant_name,</if>
            <if test="qrcodeContent != null and qrcodeContent != ''">qrcode_content,</if>
            <if test="qrcodeUrl != null">qrcode_url,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">#{merchantId},</if>
            <if test="merchantName != null and merchantName != ''">#{merchantName},</if>
            <if test="qrcodeContent != null and qrcodeContent != ''">#{qrcodeContent},</if>
            <if test="qrcodeUrl != null">#{qrcodeUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMerchantQrcode" parameterType="MerchantQrcode">
        update merchant_qrcode
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="merchantName != null and merchantName != ''">merchant_name = #{merchantName},</if>
            <if test="qrcodeContent != null and qrcodeContent != ''">qrcode_content = #{qrcodeContent},</if>
            <if test="qrcodeUrl != null">qrcode_url = #{qrcodeUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where qrcode_id = #{qrcodeId}
    </update>

    <delete id="deleteMerchantQrcodeByQrcodeId" parameterType="Long">
        delete from merchant_qrcode where qrcode_id = #{qrcodeId}
    </delete>

    <delete id="deleteMerchantQrcodeByQrcodeIds" parameterType="String">
        delete from merchant_qrcode where qrcode_id in 
        <foreach item="qrcodeId" collection="array" open="(" separator="," close=")">
            #{qrcodeId}
        </foreach>
    </delete>

</mapper>
