<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppUserProfileMapper">
    
    <resultMap type="AppUserProfile" id="AppUserProfileResult">
        <result property="profileId"    column="profile_id"    />
        <result property="userId"    column="user_id"    />
        <result property="creditScore"    column="credit_score"    />
        <result property="taskScore"    column="task_score"    />
        <result property="povertyReliefBadge"    column="poverty_relief_badge"    />
        <result property="profileDesc"    column="profile_desc"    />
        <result property="profileImages"    column="profile_images"    />
        <result property="profileVideo"    column="profile_video"    />
        <result property="totalTasks"    column="total_tasks"    />
        <result property="completedTasks"    column="completed_tasks"    />
        <result property="successRate"    column="success_rate"    />
        <result property="totalEarnings"    column="total_earnings"    />
        <result property="level"    column="level"    />
        <result property="experience"    column="experience"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppUserProfileVo">
        select profile_id, user_id, credit_score, task_score, poverty_relief_badge, profile_desc, profile_images, profile_video, total_tasks, completed_tasks, success_rate, total_earnings, level, experience, status, create_by, create_time, update_by, update_time, remark from app_user_profile
    </sql>

    <select id="selectAppUserProfileList" parameterType="AppUserProfile" resultMap="AppUserProfileResult">
        <include refid="selectAppUserProfileVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="creditScore != null "> and credit_score = #{creditScore}</if>
            <if test="taskScore != null "> and task_score = #{taskScore}</if>
            <if test="povertyReliefBadge != null  and povertyReliefBadge != ''"> and poverty_relief_badge = #{povertyReliefBadge}</if>
            <if test="profileDesc != null  and profileDesc != ''"> and profile_desc = #{profileDesc}</if>
            <if test="profileVideo != null  and profileVideo != ''"> and profile_video = #{profileVideo}</if>
            <if test="totalTasks != null "> and total_tasks = #{totalTasks}</if>
            <if test="completedTasks != null "> and completed_tasks = #{completedTasks}</if>
            <if test="successRate != null "> and success_rate = #{successRate}</if>
            <if test="totalEarnings != null "> and total_earnings = #{totalEarnings}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="experience != null "> and experience = #{experience}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by profile_id desc
    </select>
    
    <select id="selectAppUserProfileByProfileId" parameterType="Long" resultMap="AppUserProfileResult">
        <include refid="selectAppUserProfileVo"/>
        where profile_id = #{profileId}
    </select>

    <select id="selectAppUserProfileByUserId" parameterType="Long" resultMap="AppUserProfileResult">
        <include refid="selectAppUserProfileVo"/>
        where user_id = #{userId}
    </select>
        
    <insert id="insertAppUserProfile" parameterType="AppUserProfile" useGeneratedKeys="true" keyProperty="profileId">
        insert into app_user_profile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="creditScore != null">credit_score,</if>
            <if test="taskScore != null">task_score,</if>
            <if test="povertyReliefBadge != null">poverty_relief_badge,</if>
            <if test="profileDesc != null">profile_desc,</if>
            <if test="profileImages != null">profile_images,</if>
            <if test="profileVideo != null">profile_video,</if>
            <if test="totalTasks != null">total_tasks,</if>
            <if test="completedTasks != null">completed_tasks,</if>
            <if test="successRate != null">success_rate,</if>
            <if test="totalEarnings != null">total_earnings,</if>
            <if test="level != null">level,</if>
            <if test="experience != null">experience,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="creditScore != null">#{creditScore},</if>
            <if test="taskScore != null">#{taskScore},</if>
            <if test="povertyReliefBadge != null">#{povertyReliefBadge},</if>
            <if test="profileDesc != null">#{profileDesc},</if>
            <if test="profileImages != null">#{profileImages},</if>
            <if test="profileVideo != null">#{profileVideo},</if>
            <if test="totalTasks != null">#{totalTasks},</if>
            <if test="completedTasks != null">#{completedTasks},</if>
            <if test="successRate != null">#{successRate},</if>
            <if test="totalEarnings != null">#{totalEarnings},</if>
            <if test="level != null">#{level},</if>
            <if test="experience != null">#{experience},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppUserProfile" parameterType="AppUserProfile">
        update app_user_profile
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="creditScore != null">credit_score = #{creditScore},</if>
            <if test="taskScore != null">task_score = #{taskScore},</if>
            <if test="povertyReliefBadge != null">poverty_relief_badge = #{povertyReliefBadge},</if>
            <if test="profileDesc != null">profile_desc = #{profileDesc},</if>
            <if test="profileImages != null">profile_images = #{profileImages},</if>
            <if test="profileVideo != null">profile_video = #{profileVideo},</if>
            <if test="totalTasks != null">total_tasks = #{totalTasks},</if>
            <if test="completedTasks != null">completed_tasks = #{completedTasks},</if>
            <if test="successRate != null">success_rate = #{successRate},</if>
            <if test="totalEarnings != null">total_earnings = #{totalEarnings},</if>
            <if test="level != null">level = #{level},</if>
            <if test="experience != null">experience = #{experience},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where profile_id = #{profileId}
    </update>

    <delete id="deleteAppUserProfileByProfileId" parameterType="Long">
        delete from app_user_profile where profile_id = #{profileId}
    </delete>

    <delete id="deleteAppUserProfileByProfileIds" parameterType="String">
        delete from app_user_profile where profile_id in 
        <foreach item="profileId" collection="array" open="(" separator="," close=")">
            #{profileId}
        </foreach>
    </delete>

    <update id="updateCreditScore">
        update app_user_profile set credit_score = credit_score + #{creditScore} where user_id = #{userId}
    </update>

    <update id="updateTaskScore">
        update app_user_profile set task_score = task_score + #{taskScore} where user_id = #{userId}
    </update>

    <update id="updateTaskStats">
        update app_user_profile
        set total_tasks = total_tasks + #{totalTasks},
            completed_tasks = completed_tasks + #{completedTasks},
            success_rate = case when total_tasks + #{totalTasks} > 0 then
                          round((completed_tasks + #{completedTasks}) * 100.0 / (total_tasks + #{totalTasks}), 2)
                          else 0 end,
            total_earnings = total_earnings + IFNULL(#{totalEarnings}, 0)
        where user_id = #{userId}
    </update>

    <update id="updateLevelAndExperience">
        update app_user_profile set level = #{level}, experience = #{experience} where user_id = #{userId}
    </update>

    <update id="updatePovertyReliefBadge">
        update app_user_profile set poverty_relief_badge = #{povertyReliefBadge} where user_id = #{userId}
    </update>

</mapper>
