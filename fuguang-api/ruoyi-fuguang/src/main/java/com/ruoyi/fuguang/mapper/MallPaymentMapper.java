package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.MallPayment;
import org.apache.ibatis.annotations.Param;

/**
 * 支付记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface MallPaymentMapper 
{
    /**
     * 查询支付记录
     * 
     * @param paymentId 支付记录主键
     * @return 支付记录
     */
    public MallPayment selectMallPaymentByPaymentId(Long paymentId);

    /**
     * 查询支付记录列表
     * 
     * @param mallPayment 支付记录
     * @return 支付记录集合
     */
    public List<MallPayment> selectMallPaymentList(MallPayment mallPayment);

    /**
     * 新增支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    public int insertMallPayment(MallPayment mallPayment);

    /**
     * 修改支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    public int updateMallPayment(MallPayment mallPayment);

    /**
     * 删除支付记录
     * 
     * @param paymentId 支付记录主键
     * @return 结果
     */
    public int deleteMallPaymentByPaymentId(Long paymentId);

    /**
     * 批量删除支付记录
     * 
     * @param paymentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMallPaymentByPaymentIds(Long[] paymentIds);

    /**
     * 根据订单ID查询支付记录
     * 
     * @param orderId 订单ID
     * @return 支付记录
     */
    public MallPayment selectPaymentByOrderId(Long orderId);

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 支付记录
     */
    public MallPayment selectPaymentByOrderNo(String orderNo);

    /**
     * 根据第三方交易号查询支付记录
     * 
     * @param tradeNo 第三方交易号
     * @return 支付记录
     */
    public MallPayment selectPaymentByTradeNo(String tradeNo);

    /**
     * 更新支付状态
     * 
     * @param paymentId 支付记录ID
     * @param payStatus 支付状态
     * @param tradeNo 第三方交易号
     * @return 结果
     */
    public int updatePaymentStatus(@Param("paymentId") Long paymentId, @Param("payStatus") String payStatus, @Param("tradeNo") String tradeNo);

    /**
     * 根据订单号更新支付状态
     * 
     * @param orderNo 订单号
     * @param payStatus 支付状态
     * @param tradeNo 第三方交易号
     * @return 结果
     */
    public int updatePaymentStatusByOrderNo(@Param("orderNo") String orderNo, @Param("payStatus") String payStatus, @Param("tradeNo") String tradeNo);
}
