package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppUserTimeline;

/**
 * APP用户履历时间线Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public interface AppUserTimelineMapper 
{
    /**
     * 查询APP用户履历时间线
     * 
     * @param timelineId APP用户履历时间线主键
     * @return APP用户履历时间线
     */
    public AppUserTimeline selectAppUserTimelineByTimelineId(Long timelineId);

    /**
     * 查询APP用户履历时间线列表
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return APP用户履历时间线集合
     */
    public List<AppUserTimeline> selectAppUserTimelineList(AppUserTimeline appUserTimeline);

    /**
     * 根据用户ID查询履历时间线
     * 
     * @param userId 用户ID
     * @return APP用户履历时间线集合
     */
    public List<AppUserTimeline> selectAppUserTimelineByUserId(Long userId);

    /**
     * 新增APP用户履历时间线
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return 结果
     */
    public int insertAppUserTimeline(AppUserTimeline appUserTimeline);

    /**
     * 修改APP用户履历时间线
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return 结果
     */
    public int updateAppUserTimeline(AppUserTimeline appUserTimeline);

    /**
     * 删除APP用户履历时间线
     * 
     * @param timelineId APP用户履历时间线主键
     * @return 结果
     */
    public int deleteAppUserTimelineByTimelineId(Long timelineId);

    /**
     * 批量删除APP用户履历时间线
     * 
     * @param timelineIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppUserTimelineByTimelineIds(Long[] timelineIds);
}
