package com.ruoyi.fuguang.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.CommissionBill;

/**
 * 佣金账单Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface ICommissionBillService 
{
    /**
     * 查询佣金账单
     * 
     * @param billId 佣金账单主键
     * @return 佣金账单
     */
    public CommissionBill selectCommissionBillByBillId(Long billId);

    /**
     * 查询佣金账单列表
     * 
     * @param commissionBill 佣金账单
     * @return 佣金账单集合
     */
    public List<CommissionBill> selectCommissionBillList(CommissionBill commissionBill);

    /**
     * 根据用户ID查询佣金账单列表
     * 
     * @param userId 用户ID
     * @return 佣金账单集合
     */
    public List<CommissionBill> selectCommissionBillListByUserId(Long userId);

    /**
     * 根据用户ID和年月查询佣金账单
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @return 佣金账单
     */
    public CommissionBill selectCommissionBillByUserIdAndMonth(Long userId, Integer billYear, Integer billMonth);

    /**
     * 新增佣金账单
     * 
     * @param commissionBill 佣金账单
     * @return 结果
     */
    public int insertCommissionBill(CommissionBill commissionBill);

    /**
     * 修改佣金账单
     * 
     * @param commissionBill 佣金账单
     * @return 结果
     */
    public int updateCommissionBill(CommissionBill commissionBill);

    /**
     * 批量删除佣金账单
     * 
     * @param billIds 需要删除的佣金账单主键集合
     * @return 结果
     */
    public int deleteCommissionBillByBillIds(Long[] billIds);

    /**
     * 删除佣金账单信息
     * 
     * @param billId 佣金账单主键
     * @return 结果
     */
    public int deleteCommissionBillByBillId(Long billId);

    /**
     * 创建或更新月度账单
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @return 佣金账单
     */
    public CommissionBill createOrUpdateMonthlyBill(Long userId, String userName, Integer billYear, Integer billMonth);

    /**
     * 更新任务佣金收入
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 佣金金额
     * @return 结果
     */
    public boolean updateTaskCommission(Long userId, String userName, BigDecimal amount);

    /**
     * 更新推荐奖励收入
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 奖励金额
     * @return 结果
     */
    public boolean updateRecommendReward(Long userId, String userName, BigDecimal amount);

    /**
     * 更新其他收入
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 收入金额
     * @return 结果
     */
    public boolean updateOtherIncome(Long userId, String userName, BigDecimal amount);

    /**
     * 更新提现金额和次数
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @param amount 提现金额
     * @return 结果
     */
    public boolean updateWithdrawAmount(Long userId, String userName, BigDecimal amount);

    /**
     * 获取用户月度账单统计
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @return 统计数据
     */
    public Map<String, Object> getMonthlyBillStatistics(Long userId, Integer billYear, Integer billMonth);
}
