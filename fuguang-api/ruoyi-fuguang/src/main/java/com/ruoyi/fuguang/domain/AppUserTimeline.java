package com.ruoyi.fuguang.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户履历时间线对象 app_user_timeline
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public class AppUserTimeline extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 时间线ID */
    private Long timelineId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 事件类型（register注册 task_complete完成任务 reward获得奖励 level_up升级等） */
    @Excel(name = "事件类型")
    private String eventType;

    /** 事件标题 */
    @Excel(name = "事件标题")
    private String eventTitle;

    /** 事件描述 */
    @Excel(name = "事件描述")
    private String eventDesc;

    /** 事件相关数据（JSON格式） */
    private String eventData;

    /** 事件时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime;

    /** 事件图标 */
    @Excel(name = "事件图标")
    private String icon;

    /** 事件颜色标识 */
    @Excel(name = "事件颜色标识")
    private String color;

    /** 状态（0正常 1隐藏） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=隐藏")
    private String status;

    public void setTimelineId(Long timelineId) 
    {
        this.timelineId = timelineId;
    }

    public Long getTimelineId() 
    {
        return timelineId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setEventType(String eventType) 
    {
        this.eventType = eventType;
    }

    public String getEventType() 
    {
        return eventType;
    }
    public void setEventTitle(String eventTitle) 
    {
        this.eventTitle = eventTitle;
    }

    public String getEventTitle() 
    {
        return eventTitle;
    }
    public void setEventDesc(String eventDesc) 
    {
        this.eventDesc = eventDesc;
    }

    public String getEventDesc() 
    {
        return eventDesc;
    }
    public void setEventData(String eventData) 
    {
        this.eventData = eventData;
    }

    public String getEventData() 
    {
        return eventData;
    }
    public void setEventTime(Date eventTime) 
    {
        this.eventTime = eventTime;
    }

    public Date getEventTime() 
    {
        return eventTime;
    }
    public void setIcon(String icon) 
    {
        this.icon = icon;
    }

    public String getIcon() 
    {
        return icon;
    }
    public void setColor(String color) 
    {
        this.color = color;
    }

    public String getColor() 
    {
        return color;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("timelineId", getTimelineId())
            .append("userId", getUserId())
            .append("eventType", getEventType())
            .append("eventTitle", getEventTitle())
            .append("eventDesc", getEventDesc())
            .append("eventData", getEventData())
            .append("eventTime", getEventTime())
            .append("icon", getIcon())
            .append("color", getColor())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
