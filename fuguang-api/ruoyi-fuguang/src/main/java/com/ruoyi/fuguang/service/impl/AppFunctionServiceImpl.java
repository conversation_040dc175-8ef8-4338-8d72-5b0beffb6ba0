package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppFunctionMapper;
import com.ruoyi.fuguang.domain.AppFunction;
import com.ruoyi.fuguang.service.IAppFunctionService;

/**
 * APP功能配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class AppFunctionServiceImpl implements IAppFunctionService 
{
    @Autowired
    private AppFunctionMapper appFunctionMapper;

    /**
     * 查询APP功能配置
     * 
     * @param functionId APP功能配置主键
     * @return APP功能配置
     */
    @Override
    public AppFunction selectAppFunctionByFunctionId(Long functionId)
    {
        return appFunctionMapper.selectAppFunctionByFunctionId(functionId);
    }

    /**
     * 查询APP功能配置列表
     * 
     * @param appFunction APP功能配置
     * @return APP功能配置
     */
    @Override
    public List<AppFunction> selectAppFunctionList(AppFunction appFunction)
    {
        return appFunctionMapper.selectAppFunctionList(appFunction);
    }

    /**
     * 查询启用的APP功能配置列表（按排序）
     *
     * @return APP功能配置集合
     */
    @Override
    public List<AppFunction> selectEnabledAppFunctionList()
    {
        return appFunctionMapper.selectEnabledAppFunctionList();
    }

    /**
     * 根据显示位置查询启用的APP功能配置列表
     *
     * @param displayLocation 显示位置（0首页 1我的页面）
     * @return APP功能配置集合
     */
    @Override
    public List<AppFunction> selectEnabledAppFunctionListByLocation(String displayLocation)
    {
        return appFunctionMapper.selectEnabledAppFunctionListByLocation(displayLocation);
    }

    /**
     * 新增APP功能配置
     * 
     * @param appFunction APP功能配置
     * @return 结果
     */
    @Override
    public int insertAppFunction(AppFunction appFunction)
    {
        appFunction.setCreateTime(DateUtils.getNowDate());
        return appFunctionMapper.insertAppFunction(appFunction);
    }

    /**
     * 修改APP功能配置
     * 
     * @param appFunction APP功能配置
     * @return 结果
     */
    @Override
    public int updateAppFunction(AppFunction appFunction)
    {
        appFunction.setUpdateTime(DateUtils.getNowDate());
        return appFunctionMapper.updateAppFunction(appFunction);
    }

    /**
     * 批量删除APP功能配置
     * 
     * @param functionIds 需要删除的APP功能配置主键
     * @return 结果
     */
    @Override
    public int deleteAppFunctionByFunctionIds(Long[] functionIds)
    {
        return appFunctionMapper.deleteAppFunctionByFunctionIds(functionIds);
    }

    /**
     * 删除APP功能配置信息
     * 
     * @param functionId APP功能配置主键
     * @return 结果
     */
    @Override
    public int deleteAppFunctionByFunctionId(Long functionId)
    {
        return appFunctionMapper.deleteAppFunctionByFunctionId(functionId);
    }
}
