package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.WithdrawRecord;

/**
 * 提现记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface WithdrawRecordMapper 
{
    /**
     * 查询提现记录
     * 
     * @param withdrawId 提现记录主键
     * @return 提现记录
     */
    public WithdrawRecord selectWithdrawRecordByWithdrawId(Long withdrawId);

    /**
     * 查询提现记录列表
     * 
     * @param withdrawRecord 提现记录
     * @return 提现记录集合
     */
    public List<WithdrawRecord> selectWithdrawRecordList(WithdrawRecord withdrawRecord);

    /**
     * 新增提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int insertWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 修改提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int updateWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 删除提现记录
     * 
     * @param withdrawId 提现记录主键
     * @return 结果
     */
    public int deleteWithdrawRecordByWithdrawId(Long withdrawId);

    /**
     * 批量删除提现记录
     * 
     * @param withdrawIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWithdrawRecordByWithdrawIds(Long[] withdrawIds);

    /**
     * 根据用户ID查询提现记录
     * 
     * @param userId 用户ID
     * @return 提现记录集合
     */
    public List<WithdrawRecord> selectWithdrawRecordsByUserId(Long userId);

    /**
     * 根据提现单号查询记录
     * 
     * @param withdrawNo 提现单号
     * @return 提现记录
     */
    public WithdrawRecord selectWithdrawRecordByWithdrawNo(String withdrawNo);
}
