package com.ruoyi.fuguang.service.impl;

import java.util.List;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppTaskTypeMapper;
import com.ruoyi.fuguang.domain.AppTaskType;
import com.ruoyi.fuguang.service.IAppTaskTypeService;

/**
 * APP任务类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@Service
public class AppTaskTypeServiceImpl implements IAppTaskTypeService 
{
    @Autowired
    private AppTaskTypeMapper appTaskTypeMapper;

    /**
     * 查询APP任务类型
     * 
     * @param typeId APP任务类型主键
     * @return APP任务类型
     */
    @Override
    public AppTaskType selectAppTaskTypeByTypeId(Long typeId)
    {
        return appTaskTypeMapper.selectAppTaskTypeByTypeId(typeId);
    }

    /**
     * 查询APP任务类型列表
     * 
     * @param appTaskType APP任务类型
     * @return APP任务类型
     */
    @Override
    public List<AppTaskType> selectAppTaskTypeList(AppTaskType appTaskType)
    {
        return appTaskTypeMapper.selectAppTaskTypeList(appTaskType);
    }

    /**
     * 新增APP任务类型
     * 
     * @param appTaskType APP任务类型
     * @return 结果
     */
    @Override
    public int insertAppTaskType(AppTaskType appTaskType)
    {
        appTaskType.setCreateTime(DateUtils.getNowDate());
        return appTaskTypeMapper.insertAppTaskType(appTaskType);
    }

    /**
     * 修改APP任务类型
     * 
     * @param appTaskType APP任务类型
     * @return 结果
     */
    @Override
    public int updateAppTaskType(AppTaskType appTaskType)
    {
        appTaskType.setUpdateTime(DateUtils.getNowDate());
        return appTaskTypeMapper.updateAppTaskType(appTaskType);
    }

    /**
     * 批量删除APP任务类型
     * 
     * @param typeIds 需要删除的APP任务类型主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskTypeByTypeIds(Long[] typeIds)
    {
        return appTaskTypeMapper.deleteAppTaskTypeByTypeIds(typeIds);
    }

    /**
     * 删除APP任务类型信息
     * 
     * @param typeId APP任务类型主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskTypeByTypeId(Long typeId)
    {
        return appTaskTypeMapper.deleteAppTaskTypeByTypeId(typeId);
    }

    /**
     * 查询一级任务类型列表
     * 
     * @return 一级任务类型集合
     */
    @Override
    public List<AppTaskType> selectFirstLevelTaskTypes()
    {
        return appTaskTypeMapper.selectFirstLevelTaskTypes();
    }

    /**
     * 根据父类型ID查询子类型列表
     * 
     * @param parentId 父类型ID
     * @return 子类型集合
     */
    @Override
    public List<AppTaskType> selectTaskTypesByParentId(Long parentId)
    {
        return appTaskTypeMapper.selectTaskTypesByParentId(parentId);
    }

    /**
     * 构建任务类型树形结构
     * 
     * @return 任务类型树
     */
    @Override
    public List<AppTaskType> buildTaskTypeTree()
    {
        List<AppTaskType> firstLevelTypes = selectFirstLevelTaskTypes();
        List<AppTaskType> result = new ArrayList<>();
        
        for (AppTaskType firstLevel : firstLevelTypes) {
            List<AppTaskType> children = selectTaskTypesByParentId(firstLevel.getTypeId());
            // 这里可以扩展为在AppTaskType中添加children字段来存储子类型
            result.add(firstLevel);
            result.addAll(children);
        }
        
        return result;
    }
}
