package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.MallCategory;

/**
 * 商品分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface MallCategoryMapper 
{
    /**
     * 查询商品分类
     * 
     * @param categoryId 商品分类主键
     * @return 商品分类
     */
    public MallCategory selectMallCategoryByCategoryId(Long categoryId);

    /**
     * 查询商品分类列表
     * 
     * @param mallCategory 商品分类
     * @return 商品分类集合
     */
    public List<MallCategory> selectMallCategoryList(MallCategory mallCategory);

    /**
     * 新增商品分类
     * 
     * @param mallCategory 商品分类
     * @return 结果
     */
    public int insertMallCategory(MallCategory mallCategory);

    /**
     * 修改商品分类
     * 
     * @param mallCategory 商品分类
     * @return 结果
     */
    public int updateMallCategory(MallCategory mallCategory);

    /**
     * 删除商品分类
     * 
     * @param categoryId 商品分类主键
     * @return 结果
     */
    public int deleteMallCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除商品分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMallCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 查询子分类数量
     * 
     * @param parentId 父分类ID
     * @return 子分类数量
     */
    public int selectChildrenCategoryCount(Long parentId);

    /**
     * 查询所有启用的分类（树形结构）
     * 
     * @return 分类列表
     */
    public List<MallCategory> selectEnabledCategoryTree();

    /**
     * 查询一级分类列表
     * 
     * @return 一级分类列表
     */
    public List<MallCategory> selectTopCategoryList();
}
