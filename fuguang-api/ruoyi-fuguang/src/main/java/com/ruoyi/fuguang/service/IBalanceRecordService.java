package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.BalanceRecord;

/**
 * 余额变动记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IBalanceRecordService 
{
    /**
     * 查询余额变动记录
     * 
     * @param recordId 余额变动记录主键
     * @return 余额变动记录
     */
    public BalanceRecord selectBalanceRecordByRecordId(Long recordId);

    /**
     * 查询余额变动记录列表
     * 
     * @param balanceRecord 余额变动记录
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordList(BalanceRecord balanceRecord);

    /**
     * 根据用户ID查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordListByUserId(Long userId);

    /**
     * 根据用户ID和时间范围查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 余额变动记录集合
     */
    public List<BalanceRecord> selectBalanceRecordListByUserIdAndTime(Long userId, String startTime, String endTime);

    /**
     * 新增余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    public int insertBalanceRecord(BalanceRecord balanceRecord);

    /**
     * 修改余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    public int updateBalanceRecord(BalanceRecord balanceRecord);

    /**
     * 批量删除余额变动记录
     * 
     * @param recordIds 需要删除的余额变动记录主键集合
     * @return 结果
     */
    public int deleteBalanceRecordByRecordIds(Long[] recordIds);

    /**
     * 删除余额变动记录信息
     * 
     * @param recordId 余额变动记录主键
     * @return 结果
     */
    public int deleteBalanceRecordByRecordId(Long recordId);
}
