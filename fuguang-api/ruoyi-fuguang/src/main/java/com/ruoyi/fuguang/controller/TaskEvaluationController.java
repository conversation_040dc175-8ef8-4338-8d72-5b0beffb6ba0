package com.ruoyi.fuguang.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.TaskEvaluation;
import com.ruoyi.fuguang.service.ITaskEvaluationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 任务评价Controller
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@RestController
@RequestMapping("/fuguang/evaluation")
public class TaskEvaluationController extends BaseController
{
    @Autowired
    private ITaskEvaluationService taskEvaluationService;

    /**
     * 查询任务评价列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskEvaluation taskEvaluation)
    {
        startPage();
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationList(taskEvaluation);
        return getDataTable(list);
    }

    /**
     * 导出任务评价列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:export')")
    @Log(title = "任务评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskEvaluation taskEvaluation)
    {
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationList(taskEvaluation);
        ExcelUtil<TaskEvaluation> util = new ExcelUtil<TaskEvaluation>(TaskEvaluation.class);
        util.exportExcel(response, list, "任务评价数据");
    }

    /**
     * 获取任务评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:query')")
    @GetMapping(value = "/{evaluationId}")
    public AjaxResult getInfo(@PathVariable("evaluationId") Long evaluationId)
    {
        return success(taskEvaluationService.selectTaskEvaluationByEvaluationId(evaluationId));
    }

    /**
     * 新增任务评价
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:add')")
    @Log(title = "任务评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskEvaluation taskEvaluation)
    {
        return toAjax(taskEvaluationService.insertTaskEvaluation(taskEvaluation));
    }

    /**
     * 修改任务评价
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:edit')")
    @Log(title = "任务评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskEvaluation taskEvaluation)
    {
        return toAjax(taskEvaluationService.updateTaskEvaluation(taskEvaluation));
    }

    /**
     * 删除任务评价
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:remove')")
    @Log(title = "任务评价", businessType = BusinessType.DELETE)
	@DeleteMapping("/{evaluationIds}")
    public AjaxResult remove(@PathVariable Long[] evaluationIds)
    {
        return toAjax(taskEvaluationService.deleteTaskEvaluationByEvaluationIds(evaluationIds));
    }

    /**
     * 根据任务ID查询评价
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:query')")
    @GetMapping("/task/{taskId}")
    public AjaxResult getByTaskId(@PathVariable("taskId") Long taskId)
    {
        return success(taskEvaluationService.selectTaskEvaluationByTaskId(taskId));
    }

    /**
     * 根据接单人ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:query')")
    @GetMapping("/receiver/{receiverId}")
    public AjaxResult getByReceiverId(@PathVariable("receiverId") Long receiverId)
    {
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationByReceiverId(receiverId);
        return success(list);
    }

    /**
     * 根据发单人ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:query')")
    @GetMapping("/publisher/{publisherId}")
    public AjaxResult getByPublisherId(@PathVariable("publisherId") Long publisherId)
    {
        List<TaskEvaluation> list = taskEvaluationService.selectTaskEvaluationByPublisherId(publisherId);
        return success(list);
    }

    /**
     * 获取用户评价统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:query')")
    @GetMapping("/statistics/{receiverId}")
    public AjaxResult getStatistics(@PathVariable("receiverId") Long receiverId)
    {
        Map<String, Object> statistics = taskEvaluationService.getEvaluationStatistics(receiverId);
        return success(statistics);
    }

    /**
     * 检查任务是否可以评价
     */
    @PreAuthorize("@ss.hasPermi('fuguang:evaluation:query')")
    @GetMapping("/canEvaluate/{taskId}/{publisherId}")
    public AjaxResult canEvaluate(@PathVariable("taskId") Long taskId, @PathVariable("publisherId") Long publisherId)
    {
        boolean canEvaluate = taskEvaluationService.canEvaluateTask(taskId, publisherId);
        return success(canEvaluate);
    }
}
