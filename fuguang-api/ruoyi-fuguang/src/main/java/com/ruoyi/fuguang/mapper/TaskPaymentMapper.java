package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.TaskPayment;
import org.apache.ibatis.annotations.Param;

/**
 * 任务支付记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface TaskPaymentMapper 
{
    /**
     * 查询任务支付记录
     * 
     * @param paymentId 任务支付记录主键
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByPaymentId(Long paymentId);

    /**
     * 查询任务支付记录列表
     * 
     * @param taskPayment 任务支付记录
     * @return 任务支付记录集合
     */
    public List<TaskPayment> selectTaskPaymentList(TaskPayment taskPayment);

    /**
     * 新增任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    public int insertTaskPayment(TaskPayment taskPayment);

    /**
     * 修改任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    public int updateTaskPayment(TaskPayment taskPayment);

    /**
     * 删除任务支付记录
     * 
     * @param paymentId 任务支付记录主键
     * @return 结果
     */
    public int deleteTaskPaymentByPaymentId(Long paymentId);

    /**
     * 批量删除任务支付记录
     * 
     * @param paymentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskPaymentByPaymentIds(Long[] paymentIds);

    /**
     * 根据任务ID查询支付记录
     * 
     * @param taskId 任务ID
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByTaskId(Long taskId);

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByOrderNo(String orderNo);

    /**
     * 更新支付状态
     * 
     * @param orderNo 订单号
     * @param payStatus 支付状态
     * @param tradeNo 第三方交易号
     * @return 结果
     */
    public int updatePaymentStatusByOrderNo(@Param("orderNo") String orderNo, 
                                          @Param("payStatus") String payStatus, 
                                          @Param("tradeNo") String tradeNo);

    /**
     * 根据用户ID查询支付记录列表
     * 
     * @param userId 用户ID
     * @return 任务支付记录集合
     */
    public List<TaskPayment> selectTaskPaymentListByUserId(Long userId);
}
