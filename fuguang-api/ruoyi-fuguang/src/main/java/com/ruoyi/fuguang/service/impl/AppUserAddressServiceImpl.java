package com.ruoyi.fuguang.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.AppUserAddressMapper;
import com.ruoyi.fuguang.domain.AppUserAddress;
import com.ruoyi.fuguang.service.IAppUserAddressService;

/**
 * APP用户地址Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@Service
public class AppUserAddressServiceImpl implements IAppUserAddressService 
{
    @Autowired
    private AppUserAddressMapper appUserAddressMapper;

    /**
     * 查询APP用户地址
     * 
     * @param addressId APP用户地址主键
     * @return APP用户地址
     */
    @Override
    public AppUserAddress selectAppUserAddressByAddressId(Long addressId)
    {
        return appUserAddressMapper.selectAppUserAddressByAddressId(addressId);
    }

    /**
     * 查询APP用户地址列表
     * 
     * @param appUserAddress APP用户地址
     * @return APP用户地址
     */
    @Override
    public List<AppUserAddress> selectAppUserAddressList(AppUserAddress appUserAddress)
    {
        return appUserAddressMapper.selectAppUserAddressList(appUserAddress);
    }

    /**
     * 根据用户ID查询地址列表
     * 
     * @param userId 用户ID
     * @return APP用户地址集合
     */
    @Override
    public List<AppUserAddress> selectAppUserAddressListByUserId(Long userId)
    {
        return appUserAddressMapper.selectAppUserAddressListByUserId(userId);
    }

    /**
     * 查询用户默认地址
     * 
     * @param userId 用户ID
     * @return APP用户地址
     */
    @Override
    public AppUserAddress selectDefaultAddressByUserId(Long userId)
    {
        return appUserAddressMapper.selectDefaultAddressByUserId(userId);
    }

    /**
     * 新增APP用户地址
     * 
     * @param appUserAddress APP用户地址
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAppUserAddress(AppUserAddress appUserAddress)
    {
        // 构建完整地址
        appUserAddress.setFullAddress(buildFullAddress(appUserAddress));
        
        // 如果设置为默认地址，先取消其他默认地址
        if ("1".equals(appUserAddress.getIsDefault()))
        {
            appUserAddressMapper.cancelDefaultAddressByUserId(appUserAddress.getUserId());
        }
        
        appUserAddress.setCreateTime(DateUtils.getNowDate());
        return appUserAddressMapper.insertAppUserAddress(appUserAddress);
    }

    /**
     * 修改APP用户地址
     * 
     * @param appUserAddress APP用户地址
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAppUserAddress(AppUserAddress appUserAddress)
    {
        // 构建完整地址
        appUserAddress.setFullAddress(buildFullAddress(appUserAddress));
        
        // 如果设置为默认地址，先取消其他默认地址
        if ("1".equals(appUserAddress.getIsDefault()))
        {
            appUserAddressMapper.cancelDefaultAddressByUserId(appUserAddress.getUserId());
        }
        
        appUserAddress.setUpdateTime(DateUtils.getNowDate());
        return appUserAddressMapper.updateAppUserAddress(appUserAddress);
    }

    /**
     * 批量删除APP用户地址
     * 
     * @param addressIds 需要删除的APP用户地址主键
     * @return 结果
     */
    @Override
    public int deleteAppUserAddressByAddressIds(Long[] addressIds)
    {
        return appUserAddressMapper.deleteAppUserAddressByAddressIds(addressIds);
    }

    /**
     * 删除APP用户地址信息
     * 
     * @param addressId APP用户地址主键
     * @return 结果
     */
    @Override
    public int deleteAppUserAddressByAddressId(Long addressId)
    {
        return appUserAddressMapper.deleteAppUserAddressByAddressId(addressId);
    }

    /**
     * 设置默认地址
     * 
     * @param addressId 地址ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int setDefaultAddress(Long addressId, Long userId)
    {
        // 先取消所有默认地址
        appUserAddressMapper.cancelDefaultAddressByUserId(userId);
        
        // 设置新的默认地址
        AppUserAddress address = new AppUserAddress();
        address.setAddressId(addressId);
        address.setIsDefault("1");
        address.setUpdateTime(DateUtils.getNowDate());
        
        return appUserAddressMapper.updateAppUserAddress(address);
    }

    /**
     * 检查联系人手机号是否唯一
     * 
     * @param appUserAddress APP用户地址
     * @return 结果
     */
    @Override
    public boolean checkContactPhoneUnique(AppUserAddress appUserAddress)
    {
        Long addressId = StringUtils.isNull(appUserAddress.getAddressId()) ? -1L : appUserAddress.getAddressId();
        AppUserAddress address = appUserAddressMapper.checkContactPhoneUnique(appUserAddress);
        if (StringUtils.isNotNull(address) && address.getAddressId().longValue() != addressId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验地址数据是否有效
     * 
     * @param appUserAddress APP用户地址
     * @return 结果
     */
    @Override
    public String validateAddressData(AppUserAddress appUserAddress)
    {
        if (StringUtils.isEmpty(appUserAddress.getContactName()))
        {
            return "联系人姓名不能为空";
        }
        if (StringUtils.isEmpty(appUserAddress.getContactPhone()))
        {
            return "联系人手机号不能为空";
        }
        if (StringUtils.isEmpty(appUserAddress.getAddress()))
        {
            return "详细地址不能为空";
        }
        
        // 验证手机号格式
        if (!appUserAddress.getContactPhone().matches("^1[3-9]\\d{9}$"))
        {
            return "手机号格式不正确";
        }
        
        return null;
    }

    /**
     * 构建完整地址
     * 
     * @param appUserAddress APP用户地址
     * @return 完整地址
     */
    @Override
    public String buildFullAddress(AppUserAddress appUserAddress)
    {
        StringBuilder fullAddress = new StringBuilder();
        
        if (StringUtils.isNotEmpty(appUserAddress.getProvince()))
        {
            fullAddress.append(appUserAddress.getProvince());
        }
        if (StringUtils.isNotEmpty(appUserAddress.getCity()))
        {
            fullAddress.append(appUserAddress.getCity());
        }
        if (StringUtils.isNotEmpty(appUserAddress.getDistrict()))
        {
            fullAddress.append(appUserAddress.getDistrict());
        }
        if (StringUtils.isNotEmpty(appUserAddress.getAddress()))
        {
            fullAddress.append(appUserAddress.getAddress());
        }
        
        return fullAddress.toString();
    }
}
