package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.MallOrderLogistics;

/**
 * 订单物流Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface MallOrderLogisticsMapper 
{
    /**
     * 查询订单物流
     * 
     * @param logisticsId 订单物流主键
     * @return 订单物流
     */
    public MallOrderLogistics selectMallOrderLogisticsByLogisticsId(Long logisticsId);

    /**
     * 查询订单物流列表
     * 
     * @param mallOrderLogistics 订单物流
     * @return 订单物流集合
     */
    public List<MallOrderLogistics> selectMallOrderLogisticsList(MallOrderLogistics mallOrderLogistics);

    /**
     * 根据订单ID查询物流信息
     * 
     * @param orderId 订单ID
     * @return 订单物流集合
     */
    public List<MallOrderLogistics> selectMallOrderLogisticsByOrderId(Long orderId);

    /**
     * 根据订单号查询物流信息
     * 
     * @param orderNo 订单号
     * @return 订单物流集合
     */
    public List<MallOrderLogistics> selectMallOrderLogisticsByOrderNo(String orderNo);

    /**
     * 新增订单物流
     * 
     * @param mallOrderLogistics 订单物流
     * @return 结果
     */
    public int insertMallOrderLogistics(MallOrderLogistics mallOrderLogistics);

    /**
     * 修改订单物流
     * 
     * @param mallOrderLogistics 订单物流
     * @return 结果
     */
    public int updateMallOrderLogistics(MallOrderLogistics mallOrderLogistics);

    /**
     * 删除订单物流
     * 
     * @param logisticsId 订单物流主键
     * @return 结果
     */
    public int deleteMallOrderLogisticsByLogisticsId(Long logisticsId);

    /**
     * 批量删除订单物流
     * 
     * @param logisticsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMallOrderLogisticsByLogisticsIds(Long[] logisticsIds);
}
