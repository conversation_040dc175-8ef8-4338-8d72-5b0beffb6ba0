package com.ruoyi.fuguang.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.fuguang.domain.CommissionBill;
import org.apache.ibatis.annotations.Param;

/**
 * 佣金账单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface CommissionBillMapper 
{
    /**
     * 查询佣金账单
     * 
     * @param billId 佣金账单主键
     * @return 佣金账单
     */
    public CommissionBill selectCommissionBillByBillId(Long billId);

    /**
     * 查询佣金账单列表
     * 
     * @param commissionBill 佣金账单
     * @return 佣金账单集合
     */
    public List<CommissionBill> selectCommissionBillList(CommissionBill commissionBill);

    /**
     * 根据用户ID查询佣金账单列表
     * 
     * @param userId 用户ID
     * @return 佣金账单集合
     */
    public List<CommissionBill> selectCommissionBillListByUserId(Long userId);

    /**
     * 根据用户ID和年月查询佣金账单
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @return 佣金账单
     */
    public CommissionBill selectCommissionBillByUserIdAndMonth(@Param("userId") Long userId, 
                                                              @Param("billYear") Integer billYear, 
                                                              @Param("billMonth") Integer billMonth);

    /**
     * 新增佣金账单
     * 
     * @param commissionBill 佣金账单
     * @return 结果
     */
    public int insertCommissionBill(CommissionBill commissionBill);

    /**
     * 修改佣金账单
     * 
     * @param commissionBill 佣金账单
     * @return 结果
     */
    public int updateCommissionBill(CommissionBill commissionBill);

    /**
     * 删除佣金账单
     * 
     * @param billId 佣金账单主键
     * @return 结果
     */
    public int deleteCommissionBillByBillId(Long billId);

    /**
     * 批量删除佣金账单
     * 
     * @param billIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommissionBillByBillIds(Long[] billIds);

    /**
     * 更新任务佣金收入
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @param amount 佣金金额
     * @return 结果
     */
    public int updateTaskCommission(@Param("userId") Long userId, 
                                   @Param("billYear") Integer billYear, 
                                   @Param("billMonth") Integer billMonth, 
                                   @Param("amount") BigDecimal amount);

    /**
     * 更新推荐奖励收入
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @param amount 奖励金额
     * @return 结果
     */
    public int updateRecommendReward(@Param("userId") Long userId, 
                                    @Param("billYear") Integer billYear, 
                                    @Param("billMonth") Integer billMonth, 
                                    @Param("amount") BigDecimal amount);

    /**
     * 更新其他收入
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @param amount 收入金额
     * @return 结果
     */
    public int updateOtherIncome(@Param("userId") Long userId, 
                                @Param("billYear") Integer billYear, 
                                @Param("billMonth") Integer billMonth, 
                                @Param("amount") BigDecimal amount);

    /**
     * 更新提现金额和次数
     * 
     * @param userId 用户ID
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @param amount 提现金额
     * @return 结果
     */
    public int updateWithdrawAmount(@Param("userId") Long userId, 
                                   @Param("billYear") Integer billYear, 
                                   @Param("billMonth") Integer billMonth, 
                                   @Param("amount") BigDecimal amount);
}
