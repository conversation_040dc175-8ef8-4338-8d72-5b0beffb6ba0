package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.UserBalanceMapper;
import com.ruoyi.fuguang.domain.UserBalance;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 用户余额Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class UserBalanceServiceImpl implements IUserBalanceService 
{
    @Autowired
    private UserBalanceMapper userBalanceMapper;

    @Autowired
    private IBalanceRecordService balanceRecordService;

    /**
     * 查询用户余额
     * 
     * @param balanceId 用户余额主键
     * @return 用户余额
     */
    @Override
    public UserBalance selectUserBalanceByBalanceId(Long balanceId)
    {
        return userBalanceMapper.selectUserBalanceByBalanceId(balanceId);
    }

    /**
     * 根据用户ID查询用户余额
     * 
     * @param userId 用户ID
     * @return 用户余额
     */
    @Override
    public UserBalance selectUserBalanceByUserId(Long userId)
    {
        return userBalanceMapper.selectUserBalanceByUserId(userId);
    }

    /**
     * 查询用户余额列表
     * 
     * @param userBalance 用户余额
     * @return 用户余额
     */
    @Override
    public List<UserBalance> selectUserBalanceList(UserBalance userBalance)
    {
        return userBalanceMapper.selectUserBalanceList(userBalance);
    }

    /**
     * 新增用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    @Override
    public int insertUserBalance(UserBalance userBalance)
    {
        userBalance.setCreateTime(DateUtils.getNowDate());
        return userBalanceMapper.insertUserBalance(userBalance);
    }

    /**
     * 修改用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    @Override
    public int updateUserBalance(UserBalance userBalance)
    {
        userBalance.setUpdateTime(DateUtils.getNowDate());
        return userBalanceMapper.updateUserBalance(userBalance);
    }

    /**
     * 批量删除用户余额
     * 
     * @param balanceIds 需要删除的用户余额主键
     * @return 结果
     */
    @Override
    public int deleteUserBalanceByBalanceIds(Long[] balanceIds)
    {
        return userBalanceMapper.deleteUserBalanceByBalanceIds(balanceIds);
    }

    /**
     * 删除用户余额信息
     * 
     * @param balanceId 用户余额主键
     * @return 结果
     */
    @Override
    public int deleteUserBalanceByBalanceId(Long balanceId)
    {
        return userBalanceMapper.deleteUserBalanceByBalanceId(balanceId);
    }

    /**
     * 初始化用户余额
     * 
     * @param userId 用户ID
     * @param userName 用户昵称
     * @return 用户余额
     */
    @Override
    public UserBalance initUserBalance(Long userId, String userName)
    {
        UserBalance userBalance = selectUserBalanceByUserId(userId);
        if (userBalance == null) {
            userBalance = new UserBalance();
            userBalance.setUserId(userId);
            userBalance.setUserName(userName);
            userBalance.setTotalBalance(BigDecimal.ZERO);
            userBalance.setAvailableBalance(BigDecimal.ZERO);
            userBalance.setFrozenBalance(BigDecimal.ZERO);
            userBalance.setTotalIncome(BigDecimal.ZERO);
            userBalance.setTotalWithdraw(BigDecimal.ZERO);
            insertUserBalance(userBalance);
        }
        return userBalance;
    }

    /**
     * 增加用户余额
     * 
     * @param userId 用户ID
     * @param amount 增加金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @param incomeType 收入类型
     * @return 结果
     */
    @Override
    @Transactional
    public boolean increaseBalance(Long userId, BigDecimal amount, String businessType, 
                                 Long businessId, String businessNo, String description, String incomeType)
    {
        // 获取当前余额
        UserBalance userBalance = selectUserBalanceByUserId(userId);
        if (userBalance == null) {
            return false;
        }

        BigDecimal balanceBefore = userBalance.getAvailableBalance();
        
        // 更新余额
        int result = userBalanceMapper.increaseBalance(userId, amount);
        if (result > 0) {
            // 更新累计收入
            userBalanceMapper.updateTotalIncome(userId, amount);
            
            // 记录余额变动
            BalanceRecord record = new BalanceRecord();
            record.setUserId(userId);
            record.setUserName(userBalance.getUserName());
            record.setChangeType("1"); // 收入
            record.setIncomeType(incomeType);
            record.setChangeAmount(amount);
            record.setBalanceBefore(balanceBefore);
            record.setBalanceAfter(balanceBefore.add(amount));
            record.setBusinessType(businessType);
            record.setBusinessId(businessId);
            record.setBusinessNo(businessNo);
            record.setDescription(description);
            balanceRecordService.insertBalanceRecord(record);
            
            return true;
        }
        return false;
    }

    /**
     * 减少用户余额
     * 
     * @param userId 用户ID
     * @param amount 减少金额
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param businessNo 业务单号
     * @param description 变动说明
     * @return 结果
     */
    @Override
    @Transactional
    public boolean decreaseBalance(Long userId, BigDecimal amount, String businessType, 
                                 Long businessId, String businessNo, String description)
    {
        // 获取当前余额
        UserBalance userBalance = selectUserBalanceByUserId(userId);
        if (userBalance == null || userBalance.getAvailableBalance().compareTo(amount) < 0) {
            return false;
        }

        BigDecimal balanceBefore = userBalance.getAvailableBalance();
        
        // 更新余额
        int result = userBalanceMapper.decreaseBalance(userId, amount);
        if (result > 0) {
            // 记录余额变动
            BalanceRecord record = new BalanceRecord();
            record.setUserId(userId);
            record.setUserName(userBalance.getUserName());
            record.setChangeType("2"); // 支出
            record.setChangeAmount(amount);
            record.setBalanceBefore(balanceBefore);
            record.setBalanceAfter(balanceBefore.subtract(amount));
            record.setBusinessType(businessType);
            record.setBusinessId(businessId);
            record.setBusinessNo(businessNo);
            record.setDescription(description);
            balanceRecordService.insertBalanceRecord(record);
            
            return true;
        }
        return false;
    }

    /**
     * 冻结用户余额
     * 
     * @param userId 用户ID
     * @param amount 冻结金额
     * @return 结果
     */
    @Override
    public boolean freezeBalance(Long userId, BigDecimal amount)
    {
        return userBalanceMapper.freezeBalance(userId, amount) > 0;
    }

    /**
     * 解冻用户余额
     * 
     * @param userId 用户ID
     * @param amount 解冻金额
     * @return 结果
     */
    @Override
    public boolean unfreezeBalance(Long userId, BigDecimal amount)
    {
        return userBalanceMapper.unfreezeBalance(userId, amount) > 0;
    }
}
