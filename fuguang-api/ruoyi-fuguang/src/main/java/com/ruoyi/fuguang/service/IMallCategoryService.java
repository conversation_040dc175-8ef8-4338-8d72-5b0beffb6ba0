package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.MallCategory;

/**
 * 商品分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IMallCategoryService 
{
    /**
     * 查询商品分类
     * 
     * @param categoryId 商品分类主键
     * @return 商品分类
     */
    public MallCategory selectMallCategoryByCategoryId(Long categoryId);

    /**
     * 查询商品分类列表
     * 
     * @param mallCategory 商品分类
     * @return 商品分类集合
     */
    public List<MallCategory> selectMallCategoryList(MallCategory mallCategory);

    /**
     * 新增商品分类
     * 
     * @param mallCategory 商品分类
     * @return 结果
     */
    public int insertMallCategory(MallCategory mallCategory);

    /**
     * 修改商品分类
     * 
     * @param mallCategory 商品分类
     * @return 结果
     */
    public int updateMallCategory(MallCategory mallCategory);

    /**
     * 批量删除商品分类
     * 
     * @param categoryIds 需要删除的商品分类主键集合
     * @return 结果
     */
    public int deleteMallCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 删除商品分类信息
     * 
     * @param categoryId 商品分类主键
     * @return 结果
     */
    public int deleteMallCategoryByCategoryId(Long categoryId);

    /**
     * 查询所有启用的分类（树形结构）
     * 
     * @return 分类列表
     */
    public List<MallCategory> selectEnabledCategoryTree();

    /**
     * 查询一级分类列表
     * 
     * @return 一级分类列表
     */
    public List<MallCategory> selectTopCategoryList();

    /**
     * 校验分类名称是否唯一
     * 
     * @param mallCategory 分类信息
     * @return 结果
     */
    public String checkCategoryNameUnique(MallCategory mallCategory);

    /**
     * 校验是否存在子分类
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    public boolean hasChildByCategory(Long categoryId);

    /**
     * 校验分类是否存在商品
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    public boolean checkCategoryExistProduct(Long categoryId);
}
