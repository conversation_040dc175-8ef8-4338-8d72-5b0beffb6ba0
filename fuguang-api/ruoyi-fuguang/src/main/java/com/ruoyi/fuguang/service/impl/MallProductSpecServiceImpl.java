package com.ruoyi.fuguang.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.MallProductSpecMapper;
import com.ruoyi.fuguang.domain.MallProductSpec;
import com.ruoyi.fuguang.service.IMallProductSpecService;

/**
 * 商品规格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class MallProductSpecServiceImpl implements IMallProductSpecService 
{
    @Autowired
    private MallProductSpecMapper mallProductSpecMapper;

    /**
     * 查询商品规格
     * 
     * @param specId 商品规格主键
     * @return 商品规格
     */
    @Override
    public MallProductSpec selectMallProductSpecBySpecId(Long specId)
    {
        return mallProductSpecMapper.selectMallProductSpecBySpecId(specId);
    }

    /**
     * 查询商品规格列表
     * 
     * @param mallProductSpec 商品规格
     * @return 商品规格
     */
    @Override
    public List<MallProductSpec> selectMallProductSpecList(MallProductSpec mallProductSpec)
    {
        return mallProductSpecMapper.selectMallProductSpecList(mallProductSpec);
    }

    /**
     * 根据商品ID查询规格列表
     * 
     * @param productId 商品ID
     * @return 商品规格集合
     */
    @Override
    public List<MallProductSpec> selectMallProductSpecByProductId(Long productId)
    {
        return mallProductSpecMapper.selectMallProductSpecByProductId(productId);
    }

    /**
     * 新增商品规格
     * 
     * @param mallProductSpec 商品规格
     * @return 结果
     */
    @Override
    public int insertMallProductSpec(MallProductSpec mallProductSpec)
    {
        mallProductSpec.setCreateTime(DateUtils.getNowDate());
        return mallProductSpecMapper.insertMallProductSpec(mallProductSpec);
    }

    /**
     * 修改商品规格
     * 
     * @param mallProductSpec 商品规格
     * @return 结果
     */
    @Override
    public int updateMallProductSpec(MallProductSpec mallProductSpec)
    {
        mallProductSpec.setUpdateTime(DateUtils.getNowDate());
        return mallProductSpecMapper.updateMallProductSpec(mallProductSpec);
    }

    /**
     * 批量删除商品规格
     * 
     * @param specIds 需要删除的商品规格主键
     * @return 结果
     */
    @Override
    public int deleteMallProductSpecBySpecIds(Long[] specIds)
    {
        return mallProductSpecMapper.deleteMallProductSpecBySpecIds(specIds);
    }

    /**
     * 删除商品规格信息
     * 
     * @param specId 商品规格主键
     * @return 结果
     */
    @Override
    public int deleteMallProductSpecBySpecId(Long specId)
    {
        return mallProductSpecMapper.deleteMallProductSpecBySpecId(specId);
    }

    /**
     * 根据商品ID删除规格
     * 
     * @param productId 商品ID
     * @return 结果
     */
    @Override
    public int deleteMallProductSpecByProductId(Long productId)
    {
        return mallProductSpecMapper.deleteMallProductSpecByProductId(productId);
    }
}
