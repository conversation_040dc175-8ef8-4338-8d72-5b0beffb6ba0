package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppUserTimelineMapper;
import com.ruoyi.fuguang.domain.AppUserTimeline;
import com.ruoyi.fuguang.service.IAppUserTimelineService;

/**
 * APP用户履历时间线Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Service
public class AppUserTimelineServiceImpl implements IAppUserTimelineService 
{
    @Autowired
    private AppUserTimelineMapper appUserTimelineMapper;

    /**
     * 查询APP用户履历时间线
     * 
     * @param timelineId APP用户履历时间线主键
     * @return APP用户履历时间线
     */
    @Override
    public AppUserTimeline selectAppUserTimelineByTimelineId(Long timelineId)
    {
        return appUserTimelineMapper.selectAppUserTimelineByTimelineId(timelineId);
    }

    /**
     * 查询APP用户履历时间线列表
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return APP用户履历时间线
     */
    @Override
    public List<AppUserTimeline> selectAppUserTimelineList(AppUserTimeline appUserTimeline)
    {
        return appUserTimelineMapper.selectAppUserTimelineList(appUserTimeline);
    }

    /**
     * 根据用户ID查询履历时间线
     * 
     * @param userId 用户ID
     * @return APP用户履历时间线集合
     */
    @Override
    public List<AppUserTimeline> selectAppUserTimelineByUserId(Long userId)
    {
        return appUserTimelineMapper.selectAppUserTimelineByUserId(userId);
    }

    /**
     * 新增APP用户履历时间线
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return 结果
     */
    @Override
    public int insertAppUserTimeline(AppUserTimeline appUserTimeline)
    {
        appUserTimeline.setCreateTime(DateUtils.getNowDate());
        return appUserTimelineMapper.insertAppUserTimeline(appUserTimeline);
    }

    /**
     * 修改APP用户履历时间线
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return 结果
     */
    @Override
    public int updateAppUserTimeline(AppUserTimeline appUserTimeline)
    {
        appUserTimeline.setUpdateTime(DateUtils.getNowDate());
        return appUserTimelineMapper.updateAppUserTimeline(appUserTimeline);
    }

    /**
     * 批量删除APP用户履历时间线
     * 
     * @param timelineIds 需要删除的APP用户履历时间线主键
     * @return 结果
     */
    @Override
    public int deleteAppUserTimelineByTimelineIds(Long[] timelineIds)
    {
        return appUserTimelineMapper.deleteAppUserTimelineByTimelineIds(timelineIds);
    }

    /**
     * 删除APP用户履历时间线信息
     * 
     * @param timelineId APP用户履历时间线主键
     * @return 结果
     */
    @Override
    public int deleteAppUserTimelineByTimelineId(Long timelineId)
    {
        return appUserTimelineMapper.deleteAppUserTimelineByTimelineId(timelineId);
    }

    /**
     * 添加用户注册事件
     * 
     * @param userId 用户ID
     * @param eventTime 事件时间
     * @return 结果
     */
    @Override
    public boolean addRegisterEvent(Long userId, Date eventTime)
    {
        AppUserTimeline timeline = new AppUserTimeline();
        timeline.setUserId(userId);
        timeline.setEventType("register");
        timeline.setEventTitle("注册浮光壁垒");
        timeline.setEventDesc("欢迎加入浮光壁垒大家庭！");
        timeline.setEventData("{\"platform\":\"app\",\"version\":\"1.0\"}");
        timeline.setEventTime(eventTime);
        timeline.setIcon("user-plus");
        timeline.setColor("success");
        timeline.setStatus("0");
        timeline.setCreateBy("system");

        return insertAppUserTimeline(timeline) > 0;
    }

    /**
     * 添加任务完成事件
     * 
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param taskTitle 任务标题
     * @param amount 任务金额
     * @param eventTime 事件时间
     * @return 结果
     */
    @Override
    public boolean addTaskCompleteEvent(Long userId, Long taskId, String taskTitle, BigDecimal amount, Date eventTime)
    {
        AppUserTimeline timeline = new AppUserTimeline();
        timeline.setUserId(userId);
        timeline.setEventType("task_complete");
        timeline.setEventTitle("完成任务：" + taskTitle);
        timeline.setEventDesc("成功完成任务，获得" + amount + "元佣金");
        timeline.setEventData("{\"taskId\":" + taskId + ",\"amount\":" + amount + ",\"taskTitle\":\"" + taskTitle + "\"}");
        timeline.setEventTime(eventTime);
        timeline.setIcon("check-circle");
        timeline.setColor("primary");
        timeline.setStatus("0");
        timeline.setCreateBy("system");

        return insertAppUserTimeline(timeline) > 0;
    }

    /**
     * 添加获得奖励事件
     * 
     * @param userId 用户ID
     * @param rewardType 奖励类型
     * @param rewardName 奖励名称
     * @param eventTime 事件时间
     * @return 结果
     */
    @Override
    public boolean addRewardEvent(Long userId, String rewardType, String rewardName, Date eventTime)
    {
        AppUserTimeline timeline = new AppUserTimeline();
        timeline.setUserId(userId);
        timeline.setEventType("reward");
        timeline.setEventTitle("获得" + rewardName);
        timeline.setEventDesc("积极参与社区活动，获得" + rewardName);
        timeline.setEventData("{\"badgeType\":\"" + rewardType + "\",\"badgeName\":\"" + rewardName + "\"}");
        timeline.setEventTime(eventTime);
        timeline.setIcon("award");
        timeline.setColor("warning");
        timeline.setStatus("0");
        timeline.setCreateBy("system");

        return insertAppUserTimeline(timeline) > 0;
    }

    /**
     * 添加等级提升事件
     * 
     * @param userId 用户ID
     * @param oldLevel 旧等级
     * @param newLevel 新等级
     * @param experience 经验值
     * @param eventTime 事件时间
     * @return 结果
     */
    @Override
    public boolean addLevelUpEvent(Long userId, Integer oldLevel, Integer newLevel, Integer experience, Date eventTime)
    {
        AppUserTimeline timeline = new AppUserTimeline();
        timeline.setUserId(userId);
        timeline.setEventType("level_up");
        timeline.setEventTitle("等级提升");
        timeline.setEventDesc("恭喜您升级到" + newLevel + "级！");
        timeline.setEventData("{\"oldLevel\":" + oldLevel + ",\"newLevel\":" + newLevel + ",\"experience\":" + experience + "}");
        timeline.setEventTime(eventTime);
        timeline.setIcon("trophy");
        timeline.setColor("danger");
        timeline.setStatus("0");
        timeline.setCreateBy("system");

        return insertAppUserTimeline(timeline) > 0;
    }

    /**
     * 添加自定义事件
     * 
     * @param userId 用户ID
     * @param eventType 事件类型
     * @param eventTitle 事件标题
     * @param eventDesc 事件描述
     * @param eventData 事件数据
     * @param eventTime 事件时间
     * @param icon 图标
     * @param color 颜色
     * @return 结果
     */
    @Override
    public boolean addCustomEvent(Long userId, String eventType, String eventTitle, String eventDesc, 
                                String eventData, Date eventTime, String icon, String color)
    {
        AppUserTimeline timeline = new AppUserTimeline();
        timeline.setUserId(userId);
        timeline.setEventType(eventType);
        timeline.setEventTitle(eventTitle);
        timeline.setEventDesc(eventDesc);
        timeline.setEventData(eventData);
        timeline.setEventTime(eventTime);
        timeline.setIcon(icon);
        timeline.setColor(color);
        timeline.setStatus("0");
        timeline.setCreateBy("system");

        return insertAppUserTimeline(timeline) > 0;
    }
}
