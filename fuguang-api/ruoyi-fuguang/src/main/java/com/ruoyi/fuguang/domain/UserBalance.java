package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户余额对象 user_balance
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class UserBalance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 余额记录ID */
    private Long balanceId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String userName;

    /** 总余额 */
    @Excel(name = "总余额")
    private BigDecimal totalBalance;

    /** 可用余额 */
    @Excel(name = "可用余额")
    private BigDecimal availableBalance;

    /** 冻结余额 */
    @Excel(name = "冻结余额")
    private BigDecimal frozenBalance;

    /** 累计收入 */
    @Excel(name = "累计收入")
    private BigDecimal totalIncome;

    /** 累计提现 */
    @Excel(name = "累计提现")
    private BigDecimal totalWithdraw;

    public void setBalanceId(Long balanceId) 
    {
        this.balanceId = balanceId;
    }

    public Long getBalanceId() 
    {
        return balanceId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setTotalBalance(BigDecimal totalBalance) 
    {
        this.totalBalance = totalBalance;
    }

    public BigDecimal getTotalBalance() 
    {
        return totalBalance;
    }

    public void setAvailableBalance(BigDecimal availableBalance) 
    {
        this.availableBalance = availableBalance;
    }

    public BigDecimal getAvailableBalance() 
    {
        return availableBalance;
    }

    public void setFrozenBalance(BigDecimal frozenBalance) 
    {
        this.frozenBalance = frozenBalance;
    }

    public BigDecimal getFrozenBalance() 
    {
        return frozenBalance;
    }

    public void setTotalIncome(BigDecimal totalIncome) 
    {
        this.totalIncome = totalIncome;
    }

    public BigDecimal getTotalIncome() 
    {
        return totalIncome;
    }

    public void setTotalWithdraw(BigDecimal totalWithdraw) 
    {
        this.totalWithdraw = totalWithdraw;
    }

    public BigDecimal getTotalWithdraw() 
    {
        return totalWithdraw;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("balanceId", getBalanceId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("totalBalance", getTotalBalance())
            .append("availableBalance", getAvailableBalance())
            .append("frozenBalance", getFrozenBalance())
            .append("totalIncome", getTotalIncome())
            .append("totalWithdraw", getTotalWithdraw())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
