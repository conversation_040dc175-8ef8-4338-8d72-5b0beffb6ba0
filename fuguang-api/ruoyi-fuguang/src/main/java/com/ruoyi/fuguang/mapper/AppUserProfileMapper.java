package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppUserProfile;

/**
 * APP用户个人简介Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public interface AppUserProfileMapper 
{
    /**
     * 查询APP用户个人简介
     * 
     * @param profileId APP用户个人简介主键
     * @return APP用户个人简介
     */
    public AppUserProfile selectAppUserProfileByProfileId(Long profileId);

    /**
     * 根据用户ID查询APP用户个人简介
     * 
     * @param userId 用户ID
     * @return APP用户个人简介
     */
    public AppUserProfile selectAppUserProfileByUserId(Long userId);

    /**
     * 查询APP用户个人简介列表
     * 
     * @param appUserProfile APP用户个人简介
     * @return APP用户个人简介集合
     */
    public List<AppUserProfile> selectAppUserProfileList(AppUserProfile appUserProfile);

    /**
     * 新增APP用户个人简介
     * 
     * @param appUserProfile APP用户个人简介
     * @return 结果
     */
    public int insertAppUserProfile(AppUserProfile appUserProfile);

    /**
     * 修改APP用户个人简介
     * 
     * @param appUserProfile APP用户个人简介
     * @return 结果
     */
    public int updateAppUserProfile(AppUserProfile appUserProfile);

    /**
     * 删除APP用户个人简介
     * 
     * @param profileId APP用户个人简介主键
     * @return 结果
     */
    public int deleteAppUserProfileByProfileId(Long profileId);

    /**
     * 批量删除APP用户个人简介
     * 
     * @param profileIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppUserProfileByProfileIds(Long[] profileIds);

    /**
     * 更新用户信用分
     * 
     * @param userId 用户ID
     * @param creditScore 信用分变化值
     * @return 结果
     */
    public int updateCreditScore(Long userId, Integer creditScore);

    /**
     * 更新用户任务分
     * 
     * @param userId 用户ID
     * @param taskScore 任务分变化值
     * @return 结果
     */
    public int updateTaskScore(Long userId, Integer taskScore);

    /**
     * 更新用户任务统计
     *
     * @param userId 用户ID
     * @param totalTasks 总任务数变化值
     * @param completedTasks 完成任务数变化值
     * @param totalEarnings 总收益变化值
     * @return 结果
     */
    public int updateTaskStats(Long userId, Integer totalTasks, Integer completedTasks, java.math.BigDecimal totalEarnings);

    /**
     * 更新用户等级和经验
     * 
     * @param userId 用户ID
     * @param level 等级
     * @param experience 经验值
     * @return 结果
     */
    public int updateLevelAndExperience(Long userId, Integer level, Integer experience);

    /**
     * 设置扶贫救援徽章
     * 
     * @param userId 用户ID
     * @param povertyReliefBadge 扶贫救援标志
     * @return 结果
     */
    public int updatePovertyReliefBadge(Long userId, String povertyReliefBadge);
}
