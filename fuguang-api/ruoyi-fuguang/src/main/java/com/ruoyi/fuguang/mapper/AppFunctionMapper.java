package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppFunction;

/**
 * APP功能配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface AppFunctionMapper 
{
    /**
     * 查询APP功能配置
     * 
     * @param functionId APP功能配置主键
     * @return APP功能配置
     */
    public AppFunction selectAppFunctionByFunctionId(Long functionId);

    /**
     * 查询APP功能配置列表
     * 
     * @param appFunction APP功能配置
     * @return APP功能配置集合
     */
    public List<AppFunction> selectAppFunctionList(AppFunction appFunction);

    /**
     * 查询启用的APP功能配置列表（按排序）
     *
     * @return APP功能配置集合
     */
    public List<AppFunction> selectEnabledAppFunctionList();

    /**
     * 根据显示位置查询启用的APP功能配置列表
     *
     * @param displayLocation 显示位置（0首页 1我的页面）
     * @return APP功能配置集合
     */
    public List<AppFunction> selectEnabledAppFunctionListByLocation(String displayLocation);

    /**
     * 新增APP功能配置
     * 
     * @param appFunction APP功能配置
     * @return 结果
     */
    public int insertAppFunction(AppFunction appFunction);

    /**
     * 修改APP功能配置
     * 
     * @param appFunction APP功能配置
     * @return 结果
     */
    public int updateAppFunction(AppFunction appFunction);

    /**
     * 删除APP功能配置
     * 
     * @param functionId APP功能配置主键
     * @return 结果
     */
    public int deleteAppFunctionByFunctionId(Long functionId);

    /**
     * 批量删除APP功能配置
     * 
     * @param functionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppFunctionByFunctionIds(Long[] functionIds);
}
