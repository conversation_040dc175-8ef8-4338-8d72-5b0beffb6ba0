package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户个人简介对象 app_user_profile
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public class AppUserProfile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 简介ID */
    private Long profileId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 信用分（默认100分） */
    @Excel(name = "信用分")
    private Integer creditScore;

    /** 任务分（完成任务获得） */
    @Excel(name = "任务分")
    private Integer taskScore;

    /** 扶贫救援标志（0无 1有） */
    @Excel(name = "扶贫救援标志", readConverterExp = "0=无,1=有")
    private String povertyReliefBadge;

    /** 个人描述 */
    @Excel(name = "个人描述")
    private String profileDesc;

    /** 个人介绍图片（JSON格式存储多张图片路径） */
    private String profileImages;

    /** 个人介绍短视频路径 */
    @Excel(name = "个人介绍短视频路径")
    private String profileVideo;

    /** 总任务数 */
    @Excel(name = "总任务数")
    private Integer totalTasks;

    /** 完成任务数 */
    @Excel(name = "完成任务数")
    private Integer completedTasks;

    /** 任务成功率 */
    @Excel(name = "任务成功率")
    private BigDecimal successRate;

    /** 总收益 */
    @Excel(name = "总收益")
    private BigDecimal totalEarnings;

    /** 用户等级 */
    @Excel(name = "用户等级")
    private Integer level;

    /** 经验值 */
    @Excel(name = "经验值")
    private Integer experience;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setProfileId(Long profileId) 
    {
        this.profileId = profileId;
    }

    public Long getProfileId() 
    {
        return profileId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setCreditScore(Integer creditScore) 
    {
        this.creditScore = creditScore;
    }

    public Integer getCreditScore() 
    {
        return creditScore;
    }
    public void setTaskScore(Integer taskScore) 
    {
        this.taskScore = taskScore;
    }

    public Integer getTaskScore() 
    {
        return taskScore;
    }
    public void setPovertyReliefBadge(String povertyReliefBadge) 
    {
        this.povertyReliefBadge = povertyReliefBadge;
    }

    public String getPovertyReliefBadge() 
    {
        return povertyReliefBadge;
    }
    public void setProfileDesc(String profileDesc) 
    {
        this.profileDesc = profileDesc;
    }

    public String getProfileDesc() 
    {
        return profileDesc;
    }
    public void setProfileImages(String profileImages) 
    {
        this.profileImages = profileImages;
    }

    public String getProfileImages() 
    {
        return profileImages;
    }
    public void setProfileVideo(String profileVideo) 
    {
        this.profileVideo = profileVideo;
    }

    public String getProfileVideo() 
    {
        return profileVideo;
    }
    public void setTotalTasks(Integer totalTasks) 
    {
        this.totalTasks = totalTasks;
    }

    public Integer getTotalTasks() 
    {
        return totalTasks;
    }
    public void setCompletedTasks(Integer completedTasks) 
    {
        this.completedTasks = completedTasks;
    }

    public Integer getCompletedTasks() 
    {
        return completedTasks;
    }
    public void setSuccessRate(BigDecimal successRate) 
    {
        this.successRate = successRate;
    }

    public BigDecimal getSuccessRate() 
    {
        return successRate;
    }
    public void setTotalEarnings(BigDecimal totalEarnings) 
    {
        this.totalEarnings = totalEarnings;
    }

    public BigDecimal getTotalEarnings() 
    {
        return totalEarnings;
    }
    public void setLevel(Integer level) 
    {
        this.level = level;
    }

    public Integer getLevel() 
    {
        return level;
    }
    public void setExperience(Integer experience) 
    {
        this.experience = experience;
    }

    public Integer getExperience() 
    {
        return experience;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("profileId", getProfileId())
            .append("userId", getUserId())
            .append("creditScore", getCreditScore())
            .append("taskScore", getTaskScore())
            .append("povertyReliefBadge", getPovertyReliefBadge())
            .append("profileDesc", getProfileDesc())
            .append("profileImages", getProfileImages())
            .append("profileVideo", getProfileVideo())
            .append("totalTasks", getTotalTasks())
            .append("completedTasks", getCompletedTasks())
            .append("successRate", getSuccessRate())
            .append("totalEarnings", getTotalEarnings())
            .append("level", getLevel())
            .append("experience", getExperience())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
