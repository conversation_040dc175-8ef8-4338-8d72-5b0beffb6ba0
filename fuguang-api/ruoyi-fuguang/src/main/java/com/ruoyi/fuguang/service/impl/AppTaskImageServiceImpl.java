package com.ruoyi.fuguang.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppTaskImageMapper;
import com.ruoyi.fuguang.domain.AppTaskImage;
import com.ruoyi.fuguang.service.IAppTaskImageService;

/**
 * APP任务图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@Service
public class AppTaskImageServiceImpl implements IAppTaskImageService 
{
    @Autowired
    private AppTaskImageMapper appTaskImageMapper;

    /**
     * 查询APP任务图片
     * 
     * @param imageId APP任务图片主键
     * @return APP任务图片
     */
    @Override
    public AppTaskImage selectAppTaskImageByImageId(Long imageId)
    {
        return appTaskImageMapper.selectAppTaskImageByImageId(imageId);
    }

    /**
     * 查询APP任务图片列表
     * 
     * @param appTaskImage APP任务图片
     * @return APP任务图片
     */
    @Override
    public List<AppTaskImage> selectAppTaskImageList(AppTaskImage appTaskImage)
    {
        return appTaskImageMapper.selectAppTaskImageList(appTaskImage);
    }

    /**
     * 根据任务ID查询任务图片列表
     * 
     * @param taskId 任务ID
     * @return 任务图片集合
     */
    @Override
    public List<AppTaskImage> selectAppTaskImagesByTaskId(Long taskId)
    {
        return appTaskImageMapper.selectAppTaskImagesByTaskId(taskId);
    }

    /**
     * 新增APP任务图片
     * 
     * @param appTaskImage APP任务图片
     * @return 结果
     */
    @Override
    public int insertAppTaskImage(AppTaskImage appTaskImage)
    {
        appTaskImage.setCreateTime(DateUtils.getNowDate());
        return appTaskImageMapper.insertAppTaskImage(appTaskImage);
    }

    /**
     * 批量新增APP任务图片
     * 
     * @param taskId 任务ID
     * @param images 图片信息列表
     * @return 结果
     */
    @Override
    public int insertAppTaskImageBatch(Long taskId, List<Object> images)
    {
        System.out.println("=== 图片批量保存调试信息 ===");
        System.out.println("任务ID: " + taskId);
        System.out.println("图片列表: " + images);

        if (images == null || images.isEmpty()) {
            System.out.println("图片列表为空，跳过保存");
            return 0;
        }

        List<AppTaskImage> taskImages = new ArrayList<>();
        for (int i = 0; i < images.size(); i++) {
            Object imageObj = images.get(i);
            System.out.println("处理第" + (i+1) + "张图片: " + imageObj);
            System.out.println("图片对象类型: " + imageObj.getClass().getName());

            if (imageObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> imageMap = (Map<String, Object>) imageObj;

                AppTaskImage taskImage = new AppTaskImage();
                taskImage.setTaskId(taskId);
                taskImage.setImageUrl((String) imageMap.get("url"));
                taskImage.setImageName((String) imageMap.get("name"));
                taskImage.setSortOrder(i);
                taskImage.setCreateTime(DateUtils.getNowDate());

                System.out.println("图片URL: " + taskImage.getImageUrl());
                System.out.println("图片名称: " + taskImage.getImageName());

                taskImages.add(taskImage);
            } else {
                System.out.println("图片对象不是Map类型，跳过");
            }
        }

        System.out.println("准备保存的图片数量: " + taskImages.size());
        if (!taskImages.isEmpty()) {
            int result = appTaskImageMapper.insertAppTaskImageBatch(taskImages);
            System.out.println("图片批量保存结果: " + result);
            return result;
        }
        System.out.println("没有有效的图片数据需要保存");
        return 0;
    }

    /**
     * 修改APP任务图片
     * 
     * @param appTaskImage APP任务图片
     * @return 结果
     */
    @Override
    public int updateAppTaskImage(AppTaskImage appTaskImage)
    {
        return appTaskImageMapper.updateAppTaskImage(appTaskImage);
    }

    /**
     * 批量删除APP任务图片
     * 
     * @param imageIds 需要删除的APP任务图片主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskImageByImageIds(Long[] imageIds)
    {
        return appTaskImageMapper.deleteAppTaskImageByImageIds(imageIds);
    }

    /**
     * 删除APP任务图片信息
     * 
     * @param imageId APP任务图片主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskImageByImageId(Long imageId)
    {
        return appTaskImageMapper.deleteAppTaskImageByImageId(imageId);
    }

    /**
     * 根据任务ID删除任务图片
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteAppTaskImagesByTaskId(Long taskId)
    {
        return appTaskImageMapper.deleteAppTaskImagesByTaskId(taskId);
    }
}
