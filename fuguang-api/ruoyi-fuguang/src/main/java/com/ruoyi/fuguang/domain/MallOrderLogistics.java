package com.ruoyi.fuguang.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单物流对象 mall_order_logistics
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public class MallOrderLogistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 物流ID */
    private Long logisticsId;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 物流类型（1发货 2退货） */
    @Excel(name = "物流类型", readConverterExp = "1=发货,2=退货")
    private String logisticsType;

    /** 物流公司 */
    @Excel(name = "物流公司")
    private String logisticsCompany;

    /** 物流单号 */
    @Excel(name = "物流单号")
    private String logisticsNo;

    /** 物流状态（0待发货 1已发货 2运输中 3已签收 4异常） */
    @Excel(name = "物流状态", readConverterExp = "0=待发货,1=已发货,2=运输中,3=已签收,4=异常")
    private String logisticsStatus;

    /** 发件人姓名 */
    @Excel(name = "发件人姓名")
    private String senderName;

    /** 发件人电话 */
    @Excel(name = "发件人电话")
    private String senderPhone;

    /** 发件地址 */
    @Excel(name = "发件地址")
    private String senderAddress;

    /** 收件人姓名 */
    @Excel(name = "收件人姓名")
    private String receiverName;

    /** 收件人电话 */
    @Excel(name = "收件人电话")
    private String receiverPhone;

    /** 收件地址 */
    @Excel(name = "收件地址")
    private String receiverAddress;

    /** 发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /** 签收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 物流跟踪信息（JSON格式） */
    @Excel(name = "物流跟踪信息")
    private String logisticsInfo;

    public void setLogisticsId(Long logisticsId) 
    {
        this.logisticsId = logisticsId;
    }

    public Long getLogisticsId() 
    {
        return logisticsId;
    }

    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }

    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }

    public void setLogisticsType(String logisticsType) 
    {
        this.logisticsType = logisticsType;
    }

    public String getLogisticsType() 
    {
        return logisticsType;
    }

    public void setLogisticsCompany(String logisticsCompany) 
    {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsCompany() 
    {
        return logisticsCompany;
    }

    public void setLogisticsNo(String logisticsNo) 
    {
        this.logisticsNo = logisticsNo;
    }

    public String getLogisticsNo() 
    {
        return logisticsNo;
    }

    public void setLogisticsStatus(String logisticsStatus) 
    {
        this.logisticsStatus = logisticsStatus;
    }

    public String getLogisticsStatus() 
    {
        return logisticsStatus;
    }

    public void setSenderName(String senderName) 
    {
        this.senderName = senderName;
    }

    public String getSenderName() 
    {
        return senderName;
    }

    public void setSenderPhone(String senderPhone) 
    {
        this.senderPhone = senderPhone;
    }

    public String getSenderPhone() 
    {
        return senderPhone;
    }

    public void setSenderAddress(String senderAddress) 
    {
        this.senderAddress = senderAddress;
    }

    public String getSenderAddress() 
    {
        return senderAddress;
    }

    public void setReceiverName(String receiverName) 
    {
        this.receiverName = receiverName;
    }

    public String getReceiverName() 
    {
        return receiverName;
    }

    public void setReceiverPhone(String receiverPhone) 
    {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverPhone() 
    {
        return receiverPhone;
    }

    public void setReceiverAddress(String receiverAddress) 
    {
        this.receiverAddress = receiverAddress;
    }

    public String getReceiverAddress() 
    {
        return receiverAddress;
    }

    public void setSendTime(Date sendTime) 
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime() 
    {
        return sendTime;
    }

    public void setReceiveTime(Date receiveTime) 
    {
        this.receiveTime = receiveTime;
    }

    public Date getReceiveTime() 
    {
        return receiveTime;
    }

    public void setLogisticsInfo(String logisticsInfo) 
    {
        this.logisticsInfo = logisticsInfo;
    }

    public String getLogisticsInfo() 
    {
        return logisticsInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logisticsId", getLogisticsId())
            .append("orderId", getOrderId())
            .append("orderNo", getOrderNo())
            .append("logisticsType", getLogisticsType())
            .append("logisticsCompany", getLogisticsCompany())
            .append("logisticsNo", getLogisticsNo())
            .append("logisticsStatus", getLogisticsStatus())
            .append("senderName", getSenderName())
            .append("senderPhone", getSenderPhone())
            .append("senderAddress", getSenderAddress())
            .append("receiverName", getReceiverName())
            .append("receiverPhone", getReceiverPhone())
            .append("receiverAddress", getReceiverAddress())
            .append("sendTime", getSendTime())
            .append("receiveTime", getReceiveTime())
            .append("logisticsInfo", getLogisticsInfo())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
