package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.OfflinePayment;

/**
 * 线下支付订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface OfflinePaymentMapper 
{
    /**
     * 查询线下支付订单
     * 
     * @param paymentId 线下支付订单主键
     * @return 线下支付订单
     */
    public OfflinePayment selectOfflinePaymentByPaymentId(Long paymentId);

    /**
     * 根据订单号查询线下支付订单
     * 
     * @param orderNo 订单号
     * @return 线下支付订单
     */
    public OfflinePayment selectOfflinePaymentByOrderNo(String orderNo);

    /**
     * 查询线下支付订单列表
     * 
     * @param offlinePayment 线下支付订单
     * @return 线下支付订单集合
     */
    public List<OfflinePayment> selectOfflinePaymentList(OfflinePayment offlinePayment);

    /**
     * 新增线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    public int insertOfflinePayment(OfflinePayment offlinePayment);

    /**
     * 修改线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    public int updateOfflinePayment(OfflinePayment offlinePayment);

    /**
     * 删除线下支付订单
     * 
     * @param paymentId 线下支付订单主键
     * @return 结果
     */
    public int deleteOfflinePaymentByPaymentId(Long paymentId);

    /**
     * 批量删除线下支付订单
     * 
     * @param paymentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOfflinePaymentByPaymentIds(Long[] paymentIds);
}
