package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商品规格对象 mall_product_spec
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public class MallProductSpec extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规格ID */
    private Long specId;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long productId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String specName;

    /** 规格图片 */
    @Excel(name = "规格图片")
    private String specImage;

    /** 售卖价格 */
    @Excel(name = "售卖价格")
    private BigDecimal salePrice;

    /** 供货价格 */
    @Excel(name = "供货价格")
    private BigDecimal supplyPrice;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private Integer stockQuantity;

    /** 规格状态（0上架 1下架） */
    @Excel(name = "规格状态", readConverterExp = "0=上架,1=下架")
    private String specStatus;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer sortOrder;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 商品名称（关联查询用） */
    private String productName;

    public void setSpecId(Long specId) 
    {
        this.specId = specId;
    }

    public Long getSpecId() 
    {
        return specId;
    }

    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }

    public void setSpecName(String specName) 
    {
        this.specName = specName;
    }

    public String getSpecName() 
    {
        return specName;
    }

    public void setSpecImage(String specImage) 
    {
        this.specImage = specImage;
    }

    public String getSpecImage() 
    {
        return specImage;
    }

    public void setSalePrice(BigDecimal salePrice) 
    {
        this.salePrice = salePrice;
    }

    public BigDecimal getSalePrice() 
    {
        return salePrice;
    }

    public void setSupplyPrice(BigDecimal supplyPrice) 
    {
        this.supplyPrice = supplyPrice;
    }

    public BigDecimal getSupplyPrice() 
    {
        return supplyPrice;
    }

    public void setStockQuantity(Integer stockQuantity) 
    {
        this.stockQuantity = stockQuantity;
    }

    public Integer getStockQuantity() 
    {
        return stockQuantity;
    }

    public void setSpecStatus(String specStatus) 
    {
        this.specStatus = specStatus;
    }

    public String getSpecStatus() 
    {
        return specStatus;
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("specId", getSpecId())
            .append("productId", getProductId())
            .append("specName", getSpecName())
            .append("specImage", getSpecImage())
            .append("salePrice", getSalePrice())
            .append("supplyPrice", getSupplyPrice())
            .append("stockQuantity", getStockQuantity())
            .append("specStatus", getSpecStatus())
            .append("sortOrder", getSortOrder())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
