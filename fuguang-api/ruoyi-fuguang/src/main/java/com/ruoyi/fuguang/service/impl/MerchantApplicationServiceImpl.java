package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.mapper.MerchantApplicationMapper;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.fuguang.service.IAppUserService;

/**
 * 商家申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class MerchantApplicationServiceImpl implements IMerchantApplicationService 
{
    @Autowired
    private MerchantApplicationMapper merchantApplicationMapper;

    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询商家申请
     * 
     * @param applicationId 商家申请主键
     * @return 商家申请
     */
    @Override
    public MerchantApplication selectMerchantApplicationByApplicationId(Long applicationId)
    {
        return merchantApplicationMapper.selectMerchantApplicationByApplicationId(applicationId);
    }

    /**
     * 根据用户ID查询商家申请
     * 
     * @param userId 用户ID
     * @return 商家申请
     */
    @Override
    public MerchantApplication selectMerchantApplicationByUserId(Long userId)
    {
        return merchantApplicationMapper.selectMerchantApplicationByUserId(userId);
    }

    /**
     * 查询商家申请列表
     * 
     * @param merchantApplication 商家申请
     * @return 商家申请
     */
    @Override
    public List<MerchantApplication> selectMerchantApplicationList(MerchantApplication merchantApplication)
    {
        return merchantApplicationMapper.selectMerchantApplicationList(merchantApplication);
    }

    /**
     * 新增商家申请
     * 
     * @param merchantApplication 商家申请
     * @return 结果
     */
    @Override
    public int insertMerchantApplication(MerchantApplication merchantApplication)
    {
        merchantApplication.setCreateTime(DateUtils.getNowDate());
        return merchantApplicationMapper.insertMerchantApplication(merchantApplication);
    }

    /**
     * 修改商家申请
     * 
     * @param merchantApplication 商家申请
     * @return 结果
     */
    @Override
    public int updateMerchantApplication(MerchantApplication merchantApplication)
    {
        merchantApplication.setUpdateTime(DateUtils.getNowDate());
        return merchantApplicationMapper.updateMerchantApplication(merchantApplication);
    }

    /**
     * 批量删除商家申请
     * 
     * @param applicationIds 需要删除的商家申请主键
     * @return 结果
     */
    @Override
    public int deleteMerchantApplicationByApplicationIds(Long[] applicationIds)
    {
        return merchantApplicationMapper.deleteMerchantApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除商家申请信息
     * 
     * @param applicationId 商家申请主键
     * @return 结果
     */
    @Override
    public int deleteMerchantApplicationByApplicationId(Long applicationId)
    {
        return merchantApplicationMapper.deleteMerchantApplicationByApplicationId(applicationId);
    }

    /**
     * 检查用户是否可以申请成为商家
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkUserCanApply(Long userId)
    {
        // 检查用户是否存在
        AppUser user = appUserService.selectAppUserByUserId(userId);
        if (user == null)
        {
            return false;
        }

        // 检查用户是否已经是商家
        if ("1".equals(user.getUserType()))
        {
            return false;
        }

        // 检查是否已有待审核或通过的申请
        int count = merchantApplicationMapper.checkUserApplicationExists(userId);
        return count == 0;
    }

    /**
     * 提交商家申请
     * 
     * @param merchantApplication 商家申请信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean submitApplication(MerchantApplication merchantApplication)
    {
        // 设置默认值
        merchantApplication.setApplicationStatus("0"); // 待审核
        merchantApplication.setStatus("0"); // 正常
        merchantApplication.setDelFlag("0"); // 未删除
        merchantApplication.setCreateTime(DateUtils.getNowDate());
        
        return merchantApplicationMapper.insertMerchantApplication(merchantApplication) > 0;
    }

    /**
     * 审核商家申请
     * 
     * @param applicationId 申请ID
     * @param applicationStatus 审核状态（1通过 2拒绝）
     * @param auditRemark 审核备注
     * @param auditBy 审核人
     * @return 结果
     */
    @Override
    @Transactional
    public boolean auditApplication(Long applicationId, String applicationStatus, String auditRemark, String auditBy)
    {
        MerchantApplication application = merchantApplicationMapper.selectMerchantApplicationByApplicationId(applicationId);
        if (application == null)
        {
            return false;
        }

        // 更新申请状态
        MerchantApplication updateApplication = new MerchantApplication();
        updateApplication.setApplicationId(applicationId);
        updateApplication.setApplicationStatus(applicationStatus);
        updateApplication.setAuditBy(auditBy);
        updateApplication.setAuditTime(new Date());
        updateApplication.setAuditRemark(auditRemark);
        updateApplication.setUpdateTime(DateUtils.getNowDate());

        int result = merchantApplicationMapper.updateMerchantApplication(updateApplication);

        // 如果审核通过，更新用户类型为商家
        if (result > 0 && "1".equals(applicationStatus))
        {
            AppUser user = new AppUser();
            user.setUserId(application.getUserId());
            user.setUserType("1"); // 商家用户
            appUserService.updateUserProfile(user);
        }

        return result > 0;
    }

    /**
     * 校验申请数据
     * 
     * @param merchantApplication 申请数据
     * @return 校验结果
     */
    @Override
    public String validateApplicationData(MerchantApplication merchantApplication)
    {
        if (merchantApplication.getUserId() == null)
        {
            return "用户ID不能为空";
        }
        
        if (StringUtils.isEmpty(merchantApplication.getBusinessLicense()))
        {
            return "营业执照不能为空";
        }
        
        if (StringUtils.isEmpty(merchantApplication.getShopName()))
        {
            return "店铺名称不能为空";
        }
        
        if (StringUtils.isEmpty(merchantApplication.getShopAddress()))
        {
            return "店铺地址不能为空";
        }
        
        if (StringUtils.isEmpty(merchantApplication.getLegalPerson()))
        {
            return "法人姓名不能为空";
        }
        
        if (StringUtils.isEmpty(merchantApplication.getLegalIdCard()))
        {
            return "法人身份证号不能为空";
        }
        
        if (StringUtils.isEmpty(merchantApplication.getLegalPhone()))
        {
            return "法人联系方式不能为空";
        }
        
        // 验证身份证号格式
        if (!merchantApplication.getLegalIdCard().matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"))
        {
            return "法人身份证号格式不正确";
        }
        
        // 验证手机号格式
        if (!merchantApplication.getLegalPhone().matches("^1[3-9]\\d{9}$"))
        {
            return "法人联系方式格式不正确";
        }
        
        return "";
    }
}
