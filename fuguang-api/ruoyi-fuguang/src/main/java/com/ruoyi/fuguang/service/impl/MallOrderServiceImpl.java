package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.MallOrderMapper;
import com.ruoyi.fuguang.mapper.MallOrderItemMapper;
import com.ruoyi.fuguang.mapper.MallProductMapper;
import com.ruoyi.fuguang.mapper.MallProductSpecMapper;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.domain.MallOrderItem;
import com.ruoyi.fuguang.domain.MallProduct;
import com.ruoyi.fuguang.domain.MallProductSpec;
import com.ruoyi.fuguang.service.IMallOrderService;
import com.ruoyi.fuguang.service.IMallOrderItemService;
import com.ruoyi.fuguang.service.IAppUserAddressService;
import com.ruoyi.fuguang.domain.AppUserAddress;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MallOrderServiceImpl implements IMallOrderService
{
    @Autowired
    private MallOrderMapper mallOrderMapper;

    @Autowired
    private MallOrderItemMapper mallOrderItemMapper;

    @Autowired
    private MallProductMapper mallProductMapper;

    @Autowired
    private MallProductSpecMapper mallProductSpecMapper;

    @Autowired
    private IMallOrderItemService mallOrderItemService;

    @Autowired
    private IAppUserAddressService appUserAddressService;

    /**
     * 查询订单
     *
     * @param orderId 订单主键
     * @return 订单
     */
    @Override
    public MallOrder selectMallOrderByOrderId(Long orderId)
    {
        return mallOrderMapper.selectMallOrderByOrderId(orderId);
    }

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单
     */
    @Override
    public MallOrder selectMallOrderByOrderNo(String orderNo)
    {
        return mallOrderMapper.selectMallOrderByOrderNo(orderNo);
    }

    /**
     * 查询订单列表
     *
     * @param mallOrder 订单
     * @return 订单
     */
    @Override
    public List<MallOrder> selectMallOrderList(MallOrder mallOrder)
    {
        return mallOrderMapper.selectMallOrderList(mallOrder);
    }

    /**
     * 新增订单
     *
     * @param mallOrder 订单
     * @return 结果
     */
    @Override
    public int insertMallOrder(MallOrder mallOrder)
    {
        mallOrder.setCreateTime(DateUtils.getNowDate());
        return mallOrderMapper.insertMallOrder(mallOrder);
    }

    /**
     * 修改订单
     *
     * @param mallOrder 订单
     * @return 结果
     */
    @Override
    public int updateMallOrder(MallOrder mallOrder)
    {
        mallOrder.setUpdateTime(DateUtils.getNowDate());
        return mallOrderMapper.updateMallOrder(mallOrder);
    }

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteMallOrderByOrderIds(Long[] orderIds)
    {
        return mallOrderMapper.deleteMallOrderByOrderIds(orderIds);
    }

    /**
     * 删除订单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public int deleteMallOrderByOrderId(Long orderId)
    {
        return mallOrderMapper.deleteMallOrderByOrderId(orderId);
    }

    /**
     * 根据用户ID查询订单列表
     *
     * @param userId 用户ID
     * @param orderStatus 订单状态（可选）
     * @return 订单列表
     */
    @Override
    public List<MallOrder> selectOrderListByUserId(Long userId, String orderStatus)
    {
        return mallOrderMapper.selectOrderListByUserId(userId, orderStatus);
    }

    /**
     * 创建订单
     *
     * @param mallOrder 订单信息
     * @return 订单信息
     */
    @Override
    @Transactional
    public MallOrder createOrder(MallOrder mallOrder)
    {
        Long userId = SecurityUtils.getUserId();
        mallOrder.setUserId(userId);

        // 1. 验证基本信息
        if (StringUtils.isNull(mallOrder.getAddressId())) {
            throw new RuntimeException("请选择收货地址");
        }

        if (mallOrder.getOrderItems() == null || mallOrder.getOrderItems().isEmpty()) {
            throw new RuntimeException("订单商品不能为空");
        }

        // 2. 验证收货地址
        AppUserAddress address = appUserAddressService.selectAppUserAddressByAddressId(mallOrder.getAddressId());
        if (address == null || !address.getUserId().equals(userId)) {
            throw new RuntimeException("收货地址不存在或无权使用");
        }

        // 3. 验证商品信息并计算金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal deliveryFee = mallOrder.getDeliveryFee() != null ? mallOrder.getDeliveryFee() : BigDecimal.ZERO;

        for (MallOrderItem item : mallOrder.getOrderItems()) {
            // 验证商品
            MallProduct product = mallProductMapper.selectMallProductByProductId(item.getProductId());
            if (product == null) {
                throw new RuntimeException("商品不存在：" + item.getProductId());
            }

            if (!"0".equals(product.getProductStatus())) {
                throw new RuntimeException("商品已下架：" + product.getProductName());
            }

            BigDecimal itemPrice;
            String specName = "";
            String specImage = "";
            Integer availableStock;

            // 如果有规格，验证规格信息
            if (item.getSpecId() != null) {
                MallProductSpec spec = mallProductSpecMapper.selectMallProductSpecBySpecId(item.getSpecId());
                if (spec == null || !spec.getProductId().equals(item.getProductId())) {
                    throw new RuntimeException("商品规格不存在");
                }

                if (!"0".equals(spec.getSpecStatus())) {
                    throw new RuntimeException("商品规格已下架：" + spec.getSpecName());
                }

                itemPrice = spec.getSalePrice();
                specName = spec.getSpecName();
                specImage = spec.getSpecImage();
                availableStock = spec.getStockQuantity();
            } else {
                itemPrice = product.getSalePrice();
                availableStock = product.getStockQuantity();
            }

            // 验证库存
            if (availableStock < item.getQuantity()) {
                throw new RuntimeException("商品库存不足：" + product.getProductName() +
                    (StringUtils.isNotEmpty(specName) ? "(" + specName + ")" : ""));
            }

            // 设置订单项信息
            item.setProductName(product.getProductName());
            item.setSpecName(specName);
            item.setProductImage(product.getProductImage());
            item.setSpecImage(specImage);
            item.setProductPrice(itemPrice);
            item.setTotalPrice(itemPrice.multiply(new BigDecimal(item.getQuantity())));

            totalAmount = totalAmount.add(item.getTotalPrice());
        }

        // 4. 设置订单信息
        mallOrder.setOrderNo(generateOrderNo());
        mallOrder.setTotalAmount(totalAmount.add(deliveryFee));
        mallOrder.setPayAmount(totalAmount.add(deliveryFee)); // 实付金额等于总金额
        mallOrder.setOrderStatus("0"); // 待付款
        mallOrder.setPayStatus("0"); // 未支付
        mallOrder.setDeliveryFee(deliveryFee);

        // 设置收货地址信息
        mallOrder.setReceiverName(address.getContactName());
        mallOrder.setReceiverPhone(address.getContactPhone());
        mallOrder.setReceiverAddress(address.getFullAddress());

        mallOrder.setCreateTime(new Date());
        mallOrder.setUpdateTime(new Date());

        // 5. 保存订单
        int result = mallOrderMapper.insertMallOrder(mallOrder);
        if (result <= 0) {
            throw new RuntimeException("订单创建失败");
        }

        // 6. 保存订单项并扣减库存
        for (MallOrderItem item : mallOrder.getOrderItems()) {
            item.setOrderId(mallOrder.getOrderId());
            item.setCreateTime(new Date());

            int itemResult = mallOrderItemMapper.insertMallOrderItem(item);
            if (itemResult <= 0) {
                throw new RuntimeException("订单项创建失败");
            }

            // 扣减库存
            if (item.getSpecId() != null) {
                // 扣减规格库存
                int stockResult = mallProductSpecMapper.updateSpecStock(item.getSpecId(), -item.getQuantity());
                if (stockResult <= 0) {
                    throw new RuntimeException("库存扣减失败");
                }
            } else {
                // 扣减商品库存
                int stockResult = mallProductMapper.updateProductStock(item.getProductId(), -item.getQuantity());
                if (stockResult <= 0) {
                    throw new RuntimeException("库存扣减失败");
                }
            }
        }

        return mallOrder;
    }

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelOrder(Long orderId, Long userId)
    {
        MallOrder order = mallOrderMapper.selectMallOrderByOrderId(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            throw new RuntimeException("订单不存在或无权操作");
        }

        if (!"0".equals(order.getOrderStatus())) {
            throw new RuntimeException("订单状态不允许取消");
        }

        // 更新订单状态
        MallOrder updateOrder = new MallOrder();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus("4"); // 已取消
        updateOrder.setCancelTime(new Date());
        updateOrder.setUpdateTime(new Date());

        int result = mallOrderMapper.updateMallOrder(updateOrder);

        if (result > 0) {
            // 恢复商品库存
            List<MallOrderItem> orderItems = mallOrderItemMapper.selectOrderItemsByOrderId(orderId);
            for (MallOrderItem item : orderItems) {
                if (item.getSpecId() != null) {
                    // 恢复规格库存
                    mallProductSpecMapper.updateSpecStock(item.getSpecId(), item.getQuantity());
                } else {
                    // 恢复商品库存
                    mallProductMapper.updateProductStock(item.getProductId(), item.getQuantity());
                }
            }
        }

        return result;
    }

    /**
     * 确认收货
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int confirmReceive(Long orderId, Long userId)
    {
        MallOrder order = mallOrderMapper.selectMallOrderByOrderId(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            throw new RuntimeException("订单不存在或无权操作");
        }

        if (!"2".equals(order.getOrderStatus())) {
            throw new RuntimeException("订单状态不允许确认收货");
        }

        MallOrder updateOrder = new MallOrder();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus("3"); // 已完成
        updateOrder.setFinishTime(new Date());
        updateOrder.setUpdateTime(new Date());

        return mallOrderMapper.updateMallOrder(updateOrder);
    }

    /**
     * 发货
     *
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int deliverOrder(Long orderId)
    {
        MallOrder order = mallOrderMapper.selectMallOrderByOrderId(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (!"1".equals(order.getOrderStatus())) {
            throw new RuntimeException("订单状态不允许发货");
        }

        MallOrder updateOrder = new MallOrder();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus("2"); // 已发货
        updateOrder.setDeliveryTime(new Date());
        updateOrder.setUpdateTime(new Date());

        return mallOrderMapper.updateMallOrder(updateOrder);
    }

    /**
     * 支付成功处理
     *
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @return 结果
     */
    @Override
    @Transactional
    public int paymentSuccess(String orderNo, String tradeNo)
    {
        MallOrder order = mallOrderMapper.selectMallOrderByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        MallOrder updateOrder = new MallOrder();
        updateOrder.setOrderId(order.getOrderId());
        updateOrder.setOrderStatus("1"); // 已付款
        updateOrder.setPayStatus("1"); // 已支付
        updateOrder.setPayTime(new Date());
        updateOrder.setUpdateTime(new Date());

        int result = mallOrderMapper.updateMallOrder(updateOrder);

        if (result > 0) {
            // 更新商品销量
            List<MallOrderItem> orderItems = mallOrderItemMapper.selectOrderItemsByOrderId(order.getOrderId());
            for (MallOrderItem item : orderItems) {
                mallProductMapper.updateProductSales(item.getProductId(), item.getQuantity());
            }
        }

        return result;
    }

    /**
     * 支付失败处理
     *
     * @param orderNo 订单号
     * @return 结果
     */
    @Override
    public int paymentFailed(String orderNo)
    {
        MallOrder order = mallOrderMapper.selectMallOrderByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        MallOrder updateOrder = new MallOrder();
        updateOrder.setOrderId(order.getOrderId());
        updateOrder.setPayStatus("2"); // 支付失败
        updateOrder.setUpdateTime(new Date());

        return mallOrderMapper.updateMallOrder(updateOrder);
    }

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    @Override
    public String generateOrderNo()
    {
        return "FG" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }

    /**
     * 查询用户订单统计
     *
     * @param userId 用户ID
     * @return 订单统计信息
     */
    @Override
    public Object getUserOrderStats(Long userId)
    {
        Map<String, Object> stats = new HashMap<>();

        // 待付款订单数
        stats.put("waitPayCount", mallOrderMapper.countOrdersByUserIdAndStatus(userId, "0"));

        // 待发货订单数
        stats.put("waitDeliveryCount", mallOrderMapper.countOrdersByUserIdAndStatus(userId, "1"));

        // 待收货订单数
        stats.put("waitReceiveCount", mallOrderMapper.countOrdersByUserIdAndStatus(userId, "2"));

        // 已完成订单数
        stats.put("finishedCount", mallOrderMapper.countOrdersByUserIdAndStatus(userId, "3"));

        return stats;
    }
}