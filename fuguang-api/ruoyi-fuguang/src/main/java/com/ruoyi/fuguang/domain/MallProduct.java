package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商品信息对象 mall_product
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class MallProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 商品ID */
    private Long productId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String productName;

    /** 商品描述 */
    @Excel(name = "商品描述")
    private String productDesc;

    /** 商品主图 */
    @Excel(name = "商品主图")
    private String productImage;

    /** 商品图片集合（JSON格式） */
    @Excel(name = "商品图片集合")
    private String productImages;

    /** 原价 */
    @Excel(name = "原价")
    private BigDecimal originalPrice;

    /** 销售价格 */
    @Excel(name = "销售价格")
    private BigDecimal salePrice;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private Integer stockQuantity;

    /** 销量 */
    @Excel(name = "销量")
    private Integer salesCount;

    /** 商品状态（0上架 1下架） */
    @Excel(name = "商品状态", readConverterExp = "0=上架,1=下架")
    private String productStatus;

    /** 是否热门（0否 1是） */
    @Excel(name = "是否热门", readConverterExp = "0=否,1=是")
    private String isHot;

    /** 是否新品（0否 1是） */
    @Excel(name = "是否新品", readConverterExp = "0=否,1=是")
    private String isNew;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private String isRecommend;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer sortOrder;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 分类名称 */
    private String categoryName;

    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }
    public void setProductDesc(String productDesc) 
    {
        this.productDesc = productDesc;
    }

    public String getProductDesc() 
    {
        return productDesc;
    }
    public void setProductImage(String productImage) 
    {
        this.productImage = productImage;
    }

    public String getProductImage() 
    {
        return productImage;
    }
    public void setProductImages(String productImages) 
    {
        this.productImages = productImages;
    }

    public String getProductImages() 
    {
        return productImages;
    }
    public void setOriginalPrice(BigDecimal originalPrice) 
    {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getOriginalPrice() 
    {
        return originalPrice;
    }
    public void setSalePrice(BigDecimal salePrice) 
    {
        this.salePrice = salePrice;
    }

    public BigDecimal getSalePrice() 
    {
        return salePrice;
    }
    public void setStockQuantity(Integer stockQuantity) 
    {
        this.stockQuantity = stockQuantity;
    }

    public Integer getStockQuantity() 
    {
        return stockQuantity;
    }
    public void setSalesCount(Integer salesCount) 
    {
        this.salesCount = salesCount;
    }

    public Integer getSalesCount() 
    {
        return salesCount;
    }
    public void setProductStatus(String productStatus) 
    {
        this.productStatus = productStatus;
    }

    public String getProductStatus() 
    {
        return productStatus;
    }
    public void setIsHot(String isHot) 
    {
        this.isHot = isHot;
    }

    public String getIsHot() 
    {
        return isHot;
    }
    public void setIsNew(String isNew) 
    {
        this.isNew = isNew;
    }

    public String getIsNew() 
    {
        return isNew;
    }
    public void setIsRecommend(String isRecommend) 
    {
        this.isRecommend = isRecommend;
    }

    public String getIsRecommend() 
    {
        return isRecommend;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getCategoryName() 
    {
        return categoryName;
    }

    public void setCategoryName(String categoryName) 
    {
        this.categoryName = categoryName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("productId", getProductId())
            .append("categoryId", getCategoryId())
            .append("productName", getProductName())
            .append("productDesc", getProductDesc())
            .append("productImage", getProductImage())
            .append("productImages", getProductImages())
            .append("originalPrice", getOriginalPrice())
            .append("salePrice", getSalePrice())
            .append("stockQuantity", getStockQuantity())
            .append("salesCount", getSalesCount())
            .append("productStatus", getProductStatus())
            .append("isHot", getIsHot())
            .append("isNew", getIsNew())
            .append("isRecommend", getIsRecommend())
            .append("sortOrder", getSortOrder())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
