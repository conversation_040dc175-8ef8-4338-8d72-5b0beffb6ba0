package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 任务支付记录对象 task_payment
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class TaskPayment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 支付记录ID */
    private Long paymentId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 任务标题 */
    @Excel(name = "任务标题")
    private String taskTitle;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String userName;

    /** 支付订单号 */
    @Excel(name = "支付订单号")
    private String orderNo;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /** 支付方式（1支付宝 2微信 3余额） */
    @Excel(name = "支付方式", readConverterExp = "1=支付宝,2=微信,3=余额")
    private String payType;

    /** 支付状态（0待支付 1支付成功 2支付失败 3已退款） */
    @Excel(name = "支付状态", readConverterExp = "0=待支付,1=支付成功,2=支付失败,3=已退款")
    private String payStatus;

    /** 第三方交易号 */
    @Excel(name = "第三方交易号")
    private String tradeNo;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 通知时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "通知时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date notifyTime;

    public void setPaymentId(Long paymentId) 
    {
        this.paymentId = paymentId;
    }

    public Long getPaymentId() 
    {
        return paymentId;
    }

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setTaskTitle(String taskTitle) 
    {
        this.taskTitle = taskTitle;
    }

    public String getTaskTitle() 
    {
        return taskTitle;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }

    public void setPayAmount(BigDecimal payAmount) 
    {
        this.payAmount = payAmount;
    }

    public BigDecimal getPayAmount() 
    {
        return payAmount;
    }

    public void setPayType(String payType) 
    {
        this.payType = payType;
    }

    public String getPayType() 
    {
        return payType;
    }

    public void setPayStatus(String payStatus) 
    {
        this.payStatus = payStatus;
    }

    public String getPayStatus() 
    {
        return payStatus;
    }

    public void setTradeNo(String tradeNo) 
    {
        this.tradeNo = tradeNo;
    }

    public String getTradeNo() 
    {
        return tradeNo;
    }

    public void setPayTime(Date payTime) 
    {
        this.payTime = payTime;
    }

    public Date getPayTime() 
    {
        return payTime;
    }

    public void setNotifyTime(Date notifyTime) 
    {
        this.notifyTime = notifyTime;
    }

    public Date getNotifyTime() 
    {
        return notifyTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("paymentId", getPaymentId())
            .append("taskId", getTaskId())
            .append("taskTitle", getTaskTitle())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("orderNo", getOrderNo())
            .append("payAmount", getPayAmount())
            .append("payType", getPayType())
            .append("payStatus", getPayStatus())
            .append("tradeNo", getTradeNo())
            .append("payTime", getPayTime())
            .append("notifyTime", getNotifyTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
