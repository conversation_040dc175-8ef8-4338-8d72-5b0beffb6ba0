package com.ruoyi.fuguang.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.fuguang.domain.UserBalance;
import org.apache.ibatis.annotations.Param;

/**
 * 用户余额Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface UserBalanceMapper 
{
    /**
     * 查询用户余额
     * 
     * @param balanceId 用户余额主键
     * @return 用户余额
     */
    public UserBalance selectUserBalanceByBalanceId(Long balanceId);

    /**
     * 根据用户ID查询用户余额
     * 
     * @param userId 用户ID
     * @return 用户余额
     */
    public UserBalance selectUserBalanceByUserId(Long userId);

    /**
     * 查询用户余额列表
     * 
     * @param userBalance 用户余额
     * @return 用户余额集合
     */
    public List<UserBalance> selectUserBalanceList(UserBalance userBalance);

    /**
     * 新增用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    public int insertUserBalance(UserBalance userBalance);

    /**
     * 修改用户余额
     * 
     * @param userBalance 用户余额
     * @return 结果
     */
    public int updateUserBalance(UserBalance userBalance);

    /**
     * 删除用户余额
     * 
     * @param balanceId 用户余额主键
     * @return 结果
     */
    public int deleteUserBalanceByBalanceId(Long balanceId);

    /**
     * 批量删除用户余额
     * 
     * @param balanceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserBalanceByBalanceIds(Long[] balanceIds);

    /**
     * 增加用户余额
     * 
     * @param userId 用户ID
     * @param amount 增加金额
     * @return 结果
     */
    public int increaseBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 减少用户余额
     * 
     * @param userId 用户ID
     * @param amount 减少金额
     * @return 结果
     */
    public int decreaseBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 冻结用户余额
     * 
     * @param userId 用户ID
     * @param amount 冻结金额
     * @return 结果
     */
    public int freezeBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 解冻用户余额
     * 
     * @param userId 用户ID
     * @param amount 解冻金额
     * @return 结果
     */
    public int unfreezeBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 更新累计收入
     * 
     * @param userId 用户ID
     * @param amount 收入金额
     * @return 结果
     */
    public int updateTotalIncome(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 更新累计提现
     * 
     * @param userId 用户ID
     * @param amount 提现金额
     * @return 结果
     */
    public int updateTotalWithdraw(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
}
