package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.MallOrderItemMapper;
import com.ruoyi.fuguang.domain.MallOrderItem;
import com.ruoyi.fuguang.service.IMallOrderItemService;

/**
 * 订单详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MallOrderItemServiceImpl implements IMallOrderItemService 
{
    @Autowired
    private MallOrderItemMapper mallOrderItemMapper;

    /**
     * 查询订单详情
     * 
     * @param itemId 订单详情主键
     * @return 订单详情
     */
    @Override
    public MallOrderItem selectMallOrderItemByItemId(Long itemId)
    {
        return mallOrderItemMapper.selectMallOrderItemByItemId(itemId);
    }

    /**
     * 查询订单详情列表
     * 
     * @param mallOrderItem 订单详情
     * @return 订单详情
     */
    @Override
    public List<MallOrderItem> selectMallOrderItemList(MallOrderItem mallOrderItem)
    {
        return mallOrderItemMapper.selectMallOrderItemList(mallOrderItem);
    }

    /**
     * 新增订单详情
     * 
     * @param mallOrderItem 订单详情
     * @return 结果
     */
    @Override
    public int insertMallOrderItem(MallOrderItem mallOrderItem)
    {
        return mallOrderItemMapper.insertMallOrderItem(mallOrderItem);
    }

    /**
     * 修改订单详情
     * 
     * @param mallOrderItem 订单详情
     * @return 结果
     */
    @Override
    public int updateMallOrderItem(MallOrderItem mallOrderItem)
    {
        return mallOrderItemMapper.updateMallOrderItem(mallOrderItem);
    }

    /**
     * 批量删除订单详情
     * 
     * @param itemIds 需要删除的订单详情主键
     * @return 结果
     */
    @Override
    public int deleteMallOrderItemByItemIds(Long[] itemIds)
    {
        return mallOrderItemMapper.deleteMallOrderItemByItemIds(itemIds);
    }

    /**
     * 删除订单详情信息
     * 
     * @param itemId 订单详情主键
     * @return 结果
     */
    @Override
    public int deleteMallOrderItemByItemId(Long itemId)
    {
        return mallOrderItemMapper.deleteMallOrderItemByItemId(itemId);
    }

    /**
     * 根据订单ID查询订单详情列表
     * 
     * @param orderId 订单ID
     * @return 订单详情列表
     */
    @Override
    public List<MallOrderItem> selectOrderItemsByOrderId(Long orderId)
    {
        return mallOrderItemMapper.selectOrderItemsByOrderId(orderId);
    }

    /**
     * 批量新增订单详情
     * 
     * @param orderItems 订单详情列表
     * @return 结果
     */
    @Override
    public int batchInsertOrderItems(List<MallOrderItem> orderItems)
    {
        return mallOrderItemMapper.batchInsertOrderItems(orderItems);
    }

    /**
     * 根据订单ID删除订单详情
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderItemsByOrderId(Long orderId)
    {
        return mallOrderItemMapper.deleteOrderItemsByOrderId(orderId);
    }
}
