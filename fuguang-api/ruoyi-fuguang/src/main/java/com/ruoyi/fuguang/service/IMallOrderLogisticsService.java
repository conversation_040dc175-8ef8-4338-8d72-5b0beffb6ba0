package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.MallOrderLogistics;

/**
 * 订单物流Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IMallOrderLogisticsService 
{
    /**
     * 查询订单物流
     * 
     * @param logisticsId 订单物流主键
     * @return 订单物流
     */
    public MallOrderLogistics selectMallOrderLogisticsByLogisticsId(Long logisticsId);

    /**
     * 查询订单物流列表
     * 
     * @param mallOrderLogistics 订单物流
     * @return 订单物流集合
     */
    public List<MallOrderLogistics> selectMallOrderLogisticsList(MallOrderLogistics mallOrderLogistics);

    /**
     * 根据订单ID查询物流信息
     * 
     * @param orderId 订单ID
     * @return 订单物流集合
     */
    public List<MallOrderLogistics> selectMallOrderLogisticsByOrderId(Long orderId);

    /**
     * 根据订单号查询物流信息
     * 
     * @param orderNo 订单号
     * @return 订单物流集合
     */
    public List<MallOrderLogistics> selectMallOrderLogisticsByOrderNo(String orderNo);

    /**
     * 新增订单物流
     * 
     * @param mallOrderLogistics 订单物流
     * @return 结果
     */
    public int insertMallOrderLogistics(MallOrderLogistics mallOrderLogistics);

    /**
     * 修改订单物流
     * 
     * @param mallOrderLogistics 订单物流
     * @return 结果
     */
    public int updateMallOrderLogistics(MallOrderLogistics mallOrderLogistics);

    /**
     * 批量删除订单物流
     * 
     * @param logisticsIds 需要删除的订单物流主键集合
     * @return 结果
     */
    public int deleteMallOrderLogisticsByLogisticsIds(Long[] logisticsIds);

    /**
     * 删除订单物流信息
     * 
     * @param logisticsId 订单物流主键
     * @return 结果
     */
    public int deleteMallOrderLogisticsByLogisticsId(Long logisticsId);

    /**
     * 创建发货物流记录
     * 
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param logisticsCompany 物流公司
     * @param logisticsNo 物流单号
     * @return 结果
     */
    public int createDeliveryLogistics(Long orderId, String orderNo, String logisticsCompany, String logisticsNo);

    /**
     * 更新物流状态
     * 
     * @param logisticsId 物流ID
     * @param status 物流状态
     * @param logisticsInfo 物流跟踪信息
     * @return 结果
     */
    public int updateLogisticsStatus(Long logisticsId, String status, String logisticsInfo);
}
