package com.ruoyi.fuguang.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.TaskPayment;
import com.ruoyi.fuguang.domain.AppTask;

/**
 * 任务支付记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface ITaskPaymentService 
{
    /**
     * 查询任务支付记录
     * 
     * @param paymentId 任务支付记录主键
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByPaymentId(Long paymentId);

    /**
     * 查询任务支付记录列表
     * 
     * @param taskPayment 任务支付记录
     * @return 任务支付记录集合
     */
    public List<TaskPayment> selectTaskPaymentList(TaskPayment taskPayment);

    /**
     * 新增任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    public int insertTaskPayment(TaskPayment taskPayment);

    /**
     * 修改任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    public int updateTaskPayment(TaskPayment taskPayment);

    /**
     * 批量删除任务支付记录
     * 
     * @param paymentIds 需要删除的任务支付记录主键集合
     * @return 结果
     */
    public int deleteTaskPaymentByPaymentIds(Long[] paymentIds);

    /**
     * 删除任务支付记录信息
     * 
     * @param paymentId 任务支付记录主键
     * @return 结果
     */
    public int deleteTaskPaymentByPaymentId(Long paymentId);

    /**
     * 根据任务ID查询支付记录
     * 
     * @param taskId 任务ID
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByTaskId(Long taskId);

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 任务支付记录
     */
    public TaskPayment selectTaskPaymentByOrderNo(String orderNo);

    /**
     * 创建任务支付订单
     * 
     * @param task 任务信息
     * @param payType 支付方式
     * @return 支付参数
     */
    public Map<String, Object> createTaskPayOrder(AppTask task, String payType);

    /**
     * 处理支付宝支付回调
     * 
     * @param params 回调参数
     * @return 处理结果
     */
    public boolean handleAlipayCallback(Map<String, String> params);

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态
     */
    public String getPaymentStatus(String orderNo);

    /**
     * 支付成功处理
     * 
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @param payType 支付方式
     * @return 结果
     */
    public int paymentSuccess(String orderNo, String tradeNo, String payType);

    /**
     * 根据用户ID查询支付记录列表
     * 
     * @param userId 用户ID
     * @return 任务支付记录集合
     */
    public List<TaskPayment> selectTaskPaymentListByUserId(Long userId);
}
