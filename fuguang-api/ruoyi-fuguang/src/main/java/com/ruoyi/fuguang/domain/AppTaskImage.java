package com.ruoyi.fuguang.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP任务图片对象 app_task_image
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public class AppTaskImage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 图片ID */
    private Long imageId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 图片URL */
    @Excel(name = "图片URL")
    private String imageUrl;

    /** 图片名称 */
    @Excel(name = "图片名称")
    private String imageName;

    /** 图片大小（字节） */
    @Excel(name = "图片大小")
    private Long imageSize;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    public void setImageId(Long imageId) 
    {
        this.imageId = imageId;
    }

    public Long getImageId() 
    {
        return imageId;
    }

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setImageUrl(String imageUrl) 
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl() 
    {
        return imageUrl;
    }

    public void setImageName(String imageName) 
    {
        this.imageName = imageName;
    }

    public String getImageName() 
    {
        return imageName;
    }

    public void setImageSize(Long imageSize) 
    {
        this.imageSize = imageSize;
    }

    public Long getImageSize() 
    {
        return imageSize;
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("imageId", getImageId())
            .append("taskId", getTaskId())
            .append("imageUrl", getImageUrl())
            .append("imageName", getImageName())
            .append("imageSize", getImageSize())
            .append("sortOrder", getSortOrder())
            .append("createTime", getCreateTime())
            .toString();
    }
}
