package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.MallOrderItem;

/**
 * 订单详情Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IMallOrderItemService 
{
    /**
     * 查询订单详情
     * 
     * @param itemId 订单详情主键
     * @return 订单详情
     */
    public MallOrderItem selectMallOrderItemByItemId(Long itemId);

    /**
     * 查询订单详情列表
     * 
     * @param mallOrderItem 订单详情
     * @return 订单详情集合
     */
    public List<MallOrderItem> selectMallOrderItemList(MallOrderItem mallOrderItem);

    /**
     * 新增订单详情
     * 
     * @param mallOrderItem 订单详情
     * @return 结果
     */
    public int insertMallOrderItem(MallOrderItem mallOrderItem);

    /**
     * 修改订单详情
     * 
     * @param mallOrderItem 订单详情
     * @return 结果
     */
    public int updateMallOrderItem(MallOrderItem mallOrderItem);

    /**
     * 批量删除订单详情
     * 
     * @param itemIds 需要删除的订单详情主键集合
     * @return 结果
     */
    public int deleteMallOrderItemByItemIds(Long[] itemIds);

    /**
     * 删除订单详情信息
     * 
     * @param itemId 订单详情主键
     * @return 结果
     */
    public int deleteMallOrderItemByItemId(Long itemId);

    /**
     * 根据订单ID查询订单详情列表
     * 
     * @param orderId 订单ID
     * @return 订单详情列表
     */
    public List<MallOrderItem> selectOrderItemsByOrderId(Long orderId);

    /**
     * 批量新增订单详情
     * 
     * @param orderItems 订单详情列表
     * @return 结果
     */
    public int batchInsertOrderItems(List<MallOrderItem> orderItems);

    /**
     * 根据订单ID删除订单详情
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteOrderItemsByOrderId(Long orderId);
}
