package com.ruoyi.fuguang.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP配置对象 app_config
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public class AppConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 配置名称 */
    @Excel(name = "配置名称")
    private String configName;

    /** 配置键名 */
    @Excel(name = "配置键名")
    private String configKey;

    /** 配置键值 */
    @Excel(name = "配置键值")
    private String configValue;

    /** 配置图片 */
    @Excel(name = "配置图片")
    private String configImage;

    /** 系统内置（Y是 N否） */
    @Excel(name = "系统内置", readConverterExp = "Y=是,N=否")
    private String configType;

    /** 配置描述 */
    @Excel(name = "配置描述")
    private String configDesc;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }

    public void setConfigName(String configName) 
    {
        this.configName = configName;
    }

    public String getConfigName() 
    {
        return configName;
    }

    public void setConfigKey(String configKey) 
    {
        this.configKey = configKey;
    }

    public String getConfigKey() 
    {
        return configKey;
    }

    public void setConfigValue(String configValue) 
    {
        this.configValue = configValue;
    }

    public String getConfigValue()
    {
        return configValue;
    }

    public void setConfigImage(String configImage)
    {
        this.configImage = configImage;
    }

    public String getConfigImage()
    {
        return configImage;
    }

    public void setConfigType(String configType)
    {
        this.configType = configType;
    }

    public String getConfigType() 
    {
        return configType;
    }

    public void setConfigDesc(String configDesc) 
    {
        this.configDesc = configDesc;
    }

    public String getConfigDesc() 
    {
        return configDesc;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("configId", getConfigId())
            .append("configName", getConfigName())
            .append("configKey", getConfigKey())
            .append("configValue", getConfigValue())
            .append("configImage", getConfigImage())
            .append("configType", getConfigType())
            .append("configDesc", getConfigDesc())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
