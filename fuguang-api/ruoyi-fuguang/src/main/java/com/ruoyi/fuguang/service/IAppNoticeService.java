package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.AppNotice;

/**
 * APP通知Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface IAppNoticeService 
{
    /**
     * 查询APP通知
     * 
     * @param noticeId APP通知主键
     * @return APP通知
     */
    public AppNotice selectAppNoticeByNoticeId(Long noticeId);

    /**
     * 查询APP通知列表
     * 
     * @param appNotice APP通知
     * @return APP通知集合
     */
    public List<AppNotice> selectAppNoticeList(AppNotice appNotice);

    /**
     * 新增APP通知
     * 
     * @param appNotice APP通知
     * @return 结果
     */
    public int insertAppNotice(AppNotice appNotice);

    /**
     * 修改APP通知
     * 
     * @param appNotice APP通知
     * @return 结果
     */
    public int updateAppNotice(AppNotice appNotice);

    /**
     * 批量删除APP通知
     * 
     * @param noticeIds 需要删除的APP通知主键集合
     * @return 结果
     */
    public int deleteAppNoticeByNoticeIds(Long[] noticeIds);

    /**
     * 删除APP通知信息
     * 
     * @param noticeId APP通知主键
     * @return 结果
     */
    public int deleteAppNoticeByNoticeId(Long noticeId);

    /**
     * 查询用户通知列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 通知列表
     */
    public List<AppNotice> selectNoticesByUser(Long userId, Integer limit);

    /**
     * 查询最新系统通知
     * 
     * @param limit 限制数量
     * @return 通知列表
     */
    public List<AppNotice> selectLatestSystemNotices(Integer limit);

    /**
     * 标记通知为已读
     * 
     * @param noticeId 通知ID
     * @param userId 用户ID
     * @return 结果
     */
    public int markNoticeAsRead(Long noticeId, Long userId);

    /**
     * 查询未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读数量
     */
    public int countUnreadNotices(Long userId);

    /**
     * 发送系统通知
     *
     * @param title 标题
     * @param content 内容
     * @param targetType 目标类型
     * @param targetUserId 目标用户ID
     * @return 结果
     */
    public int sendSystemNotice(String title, String content, String targetType, Long targetUserId);

    /**
     * 查询用户通知列表（带筛选条件）
     *
     * @param userId 用户ID
     * @param appNotice 筛选条件
     * @return 通知列表
     */
    public List<AppNotice> selectNoticesByUserWithFilter(Long userId, AppNotice appNotice);

    /**
     * 批量标记用户通知为已读
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int markAllNoticesAsRead(Long userId);

    /**
     * 清空用户所有通知
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int clearAllNotices(Long userId);
}
