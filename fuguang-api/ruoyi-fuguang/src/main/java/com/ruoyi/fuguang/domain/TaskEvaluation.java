package com.ruoyi.fuguang.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 任务评价对象 task_evaluation
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public class TaskEvaluation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    private Long evaluationId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 任务标题 */
    @Excel(name = "任务标题")
    private String taskTitle;

    /** 发单人ID（评价人） */
    @Excel(name = "发单人ID")
    private Long publisherId;

    /** 发单人昵称 */
    @Excel(name = "发单人昵称")
    private String publisherName;

    /** 接单人ID（被评价人） */
    @Excel(name = "接单人ID")
    private Long receiverId;

    /** 接单人昵称 */
    @Excel(name = "接单人昵称")
    private String receiverName;

    /** 评分（1-5分） */
    @Excel(name = "评分")
    private Integer rating;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String evaluationContent;

    /** 评价标签（JSON格式存储） */
    @Excel(name = "评价标签")
    private String evaluationTags;

    /** 是否匿名评价（0否 1是） */
    @Excel(name = "是否匿名评价", readConverterExp = "0=否,1=是")
    private String isAnonymous;

    /** 状态（0正常 1隐藏） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=隐藏")
    private String status;

    public void setEvaluationId(Long evaluationId) 
    {
        this.evaluationId = evaluationId;
    }

    public Long getEvaluationId() 
    {
        return evaluationId;
    }

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setTaskTitle(String taskTitle) 
    {
        this.taskTitle = taskTitle;
    }

    public String getTaskTitle() 
    {
        return taskTitle;
    }

    public void setPublisherId(Long publisherId) 
    {
        this.publisherId = publisherId;
    }

    public Long getPublisherId() 
    {
        return publisherId;
    }

    public void setPublisherName(String publisherName) 
    {
        this.publisherName = publisherName;
    }

    public String getPublisherName() 
    {
        return publisherName;
    }

    public void setReceiverId(Long receiverId) 
    {
        this.receiverId = receiverId;
    }

    public Long getReceiverId() 
    {
        return receiverId;
    }

    public void setReceiverName(String receiverName) 
    {
        this.receiverName = receiverName;
    }

    public String getReceiverName() 
    {
        return receiverName;
    }

    public void setRating(Integer rating) 
    {
        this.rating = rating;
    }

    public Integer getRating() 
    {
        return rating;
    }

    public void setEvaluationContent(String evaluationContent) 
    {
        this.evaluationContent = evaluationContent;
    }

    public String getEvaluationContent() 
    {
        return evaluationContent;
    }

    public void setEvaluationTags(String evaluationTags) 
    {
        this.evaluationTags = evaluationTags;
    }

    public String getEvaluationTags() 
    {
        return evaluationTags;
    }

    public void setIsAnonymous(String isAnonymous) 
    {
        this.isAnonymous = isAnonymous;
    }

    public String getIsAnonymous() 
    {
        return isAnonymous;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("evaluationId", getEvaluationId())
            .append("taskId", getTaskId())
            .append("taskTitle", getTaskTitle())
            .append("publisherId", getPublisherId())
            .append("publisherName", getPublisherName())
            .append("receiverId", getReceiverId())
            .append("receiverName", getReceiverName())
            .append("rating", getRating())
            .append("evaluationContent", getEvaluationContent())
            .append("evaluationTags", getEvaluationTags())
            .append("isAnonymous", getIsAnonymous())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
