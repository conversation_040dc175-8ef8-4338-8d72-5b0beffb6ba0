package com.ruoyi.fuguang.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.TaskEvaluation;

/**
 * 任务评价Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public interface ITaskEvaluationService 
{
    /**
     * 查询任务评价
     * 
     * @param evaluationId 任务评价主键
     * @return 任务评价
     */
    public TaskEvaluation selectTaskEvaluationByEvaluationId(Long evaluationId);

    /**
     * 查询任务评价列表
     * 
     * @param taskEvaluation 任务评价
     * @return 任务评价集合
     */
    public List<TaskEvaluation> selectTaskEvaluationList(TaskEvaluation taskEvaluation);

    /**
     * 新增任务评价
     * 
     * @param taskEvaluation 任务评价
     * @return 结果
     */
    public int insertTaskEvaluation(TaskEvaluation taskEvaluation);

    /**
     * 修改任务评价
     * 
     * @param taskEvaluation 任务评价
     * @return 结果
     */
    public int updateTaskEvaluation(TaskEvaluation taskEvaluation);

    /**
     * 批量删除任务评价
     * 
     * @param evaluationIds 需要删除的任务评价主键集合
     * @return 结果
     */
    public int deleteTaskEvaluationByEvaluationIds(Long[] evaluationIds);

    /**
     * 删除任务评价信息
     * 
     * @param evaluationId 任务评价主键
     * @return 结果
     */
    public int deleteTaskEvaluationByEvaluationId(Long evaluationId);

    /**
     * 根据任务ID查询评价
     * 
     * @param taskId 任务ID
     * @return 任务评价
     */
    public TaskEvaluation selectTaskEvaluationByTaskId(Long taskId);

    /**
     * 根据接单人ID查询评价列表
     * 
     * @param receiverId 接单人ID
     * @return 任务评价集合
     */
    public List<TaskEvaluation> selectTaskEvaluationByReceiverId(Long receiverId);

    /**
     * 根据发单人ID查询评价列表
     * 
     * @param publisherId 发单人ID
     * @return 任务评价集合
     */
    public List<TaskEvaluation> selectTaskEvaluationByPublisherId(Long publisherId);

    /**
     * 统计接单人的平均评分
     * 
     * @param receiverId 接单人ID
     * @return 平均评分
     */
    public Double selectAverageRatingByReceiverId(Long receiverId);

    /**
     * 统计接单人的评价总数
     * 
     * @param receiverId 接单人ID
     * @return 评价总数
     */
    public Integer selectEvaluationCountByReceiverId(Long receiverId);

    /**
     * 统计各评分等级的数量
     * 
     * @param receiverId 接单人ID
     * @return 评分统计
     */
    public List<Map<String, Object>> selectRatingStatsByReceiverId(Long receiverId);

    /**
     * 提交任务评价
     * 
     * @param taskId 任务ID
     * @param publisherId 发单人ID
     * @param rating 评分
     * @param evaluationContent 评价内容
     * @param evaluationTags 评价标签
     * @param isAnonymous 是否匿名
     * @return 结果
     */
    public boolean submitTaskEvaluation(Long taskId, Long publisherId, Integer rating, 
                                      String evaluationContent, String evaluationTags, String isAnonymous);

    /**
     * 检查任务是否可以评价
     * 
     * @param taskId 任务ID
     * @param publisherId 发单人ID
     * @return 是否可以评价
     */
    public boolean canEvaluateTask(Long taskId, Long publisherId);

    /**
     * 获取用户评价统计信息
     * 
     * @param receiverId 接单人ID
     * @return 评价统计信息
     */
    public Map<String, Object> getEvaluationStatistics(Long receiverId);
}
