package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppNoticeMapper;
import com.ruoyi.fuguang.domain.AppNotice;
import com.ruoyi.fuguang.service.IAppNoticeService;

/**
 * APP通知Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class AppNoticeServiceImpl implements IAppNoticeService 
{
    @Autowired
    private AppNoticeMapper appNoticeMapper;

    /**
     * 查询APP通知
     * 
     * @param noticeId APP通知主键
     * @return APP通知
     */
    @Override
    public AppNotice selectAppNoticeByNoticeId(Long noticeId)
    {
        return appNoticeMapper.selectAppNoticeByNoticeId(noticeId);
    }

    /**
     * 查询APP通知列表
     * 
     * @param appNotice APP通知
     * @return APP通知
     */
    @Override
    public List<AppNotice> selectAppNoticeList(AppNotice appNotice)
    {
        return appNoticeMapper.selectAppNoticeList(appNotice);
    }

    /**
     * 新增APP通知
     * 
     * @param appNotice APP通知
     * @return 结果
     */
    @Override
    public int insertAppNotice(AppNotice appNotice)
    {
        appNotice.setCreateTime(DateUtils.getNowDate());
        return appNoticeMapper.insertAppNotice(appNotice);
    }

    /**
     * 修改APP通知
     * 
     * @param appNotice APP通知
     * @return 结果
     */
    @Override
    public int updateAppNotice(AppNotice appNotice)
    {
        appNotice.setUpdateTime(DateUtils.getNowDate());
        return appNoticeMapper.updateAppNotice(appNotice);
    }

    /**
     * 批量删除APP通知
     * 
     * @param noticeIds 需要删除的APP通知主键
     * @return 结果
     */
    @Override
    public int deleteAppNoticeByNoticeIds(Long[] noticeIds)
    {
        return appNoticeMapper.deleteAppNoticeByNoticeIds(noticeIds);
    }

    /**
     * 删除APP通知信息
     * 
     * @param noticeId APP通知主键
     * @return 结果
     */
    @Override
    public int deleteAppNoticeByNoticeId(Long noticeId)
    {
        return appNoticeMapper.deleteAppNoticeByNoticeId(noticeId);
    }

    /**
     * 查询用户通知列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 通知列表
     */
    @Override
    public List<AppNotice> selectNoticesByUser(Long userId, Integer limit)
    {
        return appNoticeMapper.selectNoticesByUser(userId, limit);
    }

    /**
     * 查询最新系统通知
     * 
     * @param limit 限制数量
     * @return 通知列表
     */
    @Override
    public List<AppNotice> selectLatestSystemNotices(Integer limit)
    {
        return appNoticeMapper.selectLatestSystemNotices(limit);
    }

    /**
     * 标记通知为已读
     * 
     * @param noticeId 通知ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int markNoticeAsRead(Long noticeId, Long userId)
    {
        return appNoticeMapper.markNoticeAsRead(noticeId, userId);
    }

    /**
     * 查询未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读数量
     */
    @Override
    public int countUnreadNotices(Long userId)
    {
        return appNoticeMapper.countUnreadNotices(userId);
    }

    /**
     * 发送系统通知
     *
     * @param title 标题
     * @param content 内容
     * @param targetType 目标类型
     * @param targetUserId 目标用户ID
     * @return 结果
     */
    @Override
    public int sendSystemNotice(String title, String content, String targetType, Long targetUserId)
    {
        AppNotice notice = new AppNotice();
        notice.setNoticeTitle(title);
        notice.setNoticeContent(content);
        notice.setNoticeType("0"); // 系统通知
        notice.setNoticeStatus("0"); // 正常
        notice.setTargetType(targetType);
        notice.setTargetUserId(targetUserId);
        notice.setIsRead("0"); // 未读
        notice.setPublishTime(new Date());
        notice.setCreateTime(DateUtils.getNowDate());

        return appNoticeMapper.insertAppNotice(notice);
    }

    /**
     * 查询用户通知列表（带筛选条件）
     *
     * @param userId 用户ID
     * @param appNotice 筛选条件
     * @return 通知列表
     */
    @Override
    public List<AppNotice> selectNoticesByUserWithFilter(Long userId, AppNotice appNotice)
    {
        return appNoticeMapper.selectNoticesByUserWithFilter(userId, appNotice);
    }

    /**
     * 批量标记用户通知为已读
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int markAllNoticesAsRead(Long userId)
    {
        return appNoticeMapper.markAllNoticesAsRead(userId);
    }

    /**
     * 清空用户所有通知
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int clearAllNotices(Long userId)
    {
        return appNoticeMapper.clearAllNotices(userId);
    }
}
