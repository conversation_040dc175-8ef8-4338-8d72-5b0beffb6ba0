package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.MallOrderLogisticsMapper;
import com.ruoyi.fuguang.domain.MallOrderLogistics;
import com.ruoyi.fuguang.service.IMallOrderLogisticsService;

/**
 * 订单物流Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class MallOrderLogisticsServiceImpl implements IMallOrderLogisticsService 
{
    @Autowired
    private MallOrderLogisticsMapper mallOrderLogisticsMapper;

    /**
     * 查询订单物流
     * 
     * @param logisticsId 订单物流主键
     * @return 订单物流
     */
    @Override
    public MallOrderLogistics selectMallOrderLogisticsByLogisticsId(Long logisticsId)
    {
        return mallOrderLogisticsMapper.selectMallOrderLogisticsByLogisticsId(logisticsId);
    }

    /**
     * 查询订单物流列表
     * 
     * @param mallOrderLogistics 订单物流
     * @return 订单物流
     */
    @Override
    public List<MallOrderLogistics> selectMallOrderLogisticsList(MallOrderLogistics mallOrderLogistics)
    {
        return mallOrderLogisticsMapper.selectMallOrderLogisticsList(mallOrderLogistics);
    }

    /**
     * 根据订单ID查询物流信息
     * 
     * @param orderId 订单ID
     * @return 订单物流集合
     */
    @Override
    public List<MallOrderLogistics> selectMallOrderLogisticsByOrderId(Long orderId)
    {
        return mallOrderLogisticsMapper.selectMallOrderLogisticsByOrderId(orderId);
    }

    /**
     * 根据订单号查询物流信息
     * 
     * @param orderNo 订单号
     * @return 订单物流集合
     */
    @Override
    public List<MallOrderLogistics> selectMallOrderLogisticsByOrderNo(String orderNo)
    {
        return mallOrderLogisticsMapper.selectMallOrderLogisticsByOrderNo(orderNo);
    }

    /**
     * 新增订单物流
     * 
     * @param mallOrderLogistics 订单物流
     * @return 结果
     */
    @Override
    public int insertMallOrderLogistics(MallOrderLogistics mallOrderLogistics)
    {
        mallOrderLogistics.setCreateTime(DateUtils.getNowDate());
        return mallOrderLogisticsMapper.insertMallOrderLogistics(mallOrderLogistics);
    }

    /**
     * 修改订单物流
     * 
     * @param mallOrderLogistics 订单物流
     * @return 结果
     */
    @Override
    public int updateMallOrderLogistics(MallOrderLogistics mallOrderLogistics)
    {
        mallOrderLogistics.setUpdateTime(DateUtils.getNowDate());
        return mallOrderLogisticsMapper.updateMallOrderLogistics(mallOrderLogistics);
    }

    /**
     * 批量删除订单物流
     * 
     * @param logisticsIds 需要删除的订单物流主键
     * @return 结果
     */
    @Override
    public int deleteMallOrderLogisticsByLogisticsIds(Long[] logisticsIds)
    {
        return mallOrderLogisticsMapper.deleteMallOrderLogisticsByLogisticsIds(logisticsIds);
    }

    /**
     * 删除订单物流信息
     * 
     * @param logisticsId 订单物流主键
     * @return 结果
     */
    @Override
    public int deleteMallOrderLogisticsByLogisticsId(Long logisticsId)
    {
        return mallOrderLogisticsMapper.deleteMallOrderLogisticsByLogisticsId(logisticsId);
    }

    /**
     * 创建发货物流记录
     * 
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param logisticsCompany 物流公司
     * @param logisticsNo 物流单号
     * @return 结果
     */
    @Override
    public int createDeliveryLogistics(Long orderId, String orderNo, String logisticsCompany, String logisticsNo)
    {
        MallOrderLogistics logistics = new MallOrderLogistics();
        logistics.setOrderId(orderId);
        logistics.setOrderNo(orderNo);
        logistics.setLogisticsType("1"); // 1发货
        logistics.setLogisticsCompany(logisticsCompany);
        logistics.setLogisticsNo(logisticsNo);
        logistics.setLogisticsStatus("1"); // 1已发货
        logistics.setSendTime(new Date());
        logistics.setCreateTime(DateUtils.getNowDate());
        return mallOrderLogisticsMapper.insertMallOrderLogistics(logistics);
    }

    /**
     * 更新物流状态
     * 
     * @param logisticsId 物流ID
     * @param status 物流状态
     * @param logisticsInfo 物流跟踪信息
     * @return 结果
     */
    @Override
    public int updateLogisticsStatus(Long logisticsId, String status, String logisticsInfo)
    {
        MallOrderLogistics logistics = new MallOrderLogistics();
        logistics.setLogisticsId(logisticsId);
        logistics.setLogisticsStatus(status);
        logistics.setLogisticsInfo(logisticsInfo);
        logistics.setUpdateTime(DateUtils.getNowDate());
        
        // 如果是已签收状态，设置签收时间
        if ("3".equals(status)) {
            logistics.setReceiveTime(new Date());
        }
        
        return mallOrderLogisticsMapper.updateMallOrderLogistics(logistics);
    }
}
