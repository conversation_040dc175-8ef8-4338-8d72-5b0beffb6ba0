package com.ruoyi.fuguang.service;

import java.util.Date;
import java.util.List;
import com.ruoyi.fuguang.domain.AppUserTimeline;

/**
 * APP用户履历时间线Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
public interface IAppUserTimelineService 
{
    /**
     * 查询APP用户履历时间线
     * 
     * @param timelineId APP用户履历时间线主键
     * @return APP用户履历时间线
     */
    public AppUserTimeline selectAppUserTimelineByTimelineId(Long timelineId);

    /**
     * 查询APP用户履历时间线列表
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return APP用户履历时间线集合
     */
    public List<AppUserTimeline> selectAppUserTimelineList(AppUserTimeline appUserTimeline);

    /**
     * 根据用户ID查询履历时间线
     * 
     * @param userId 用户ID
     * @return APP用户履历时间线集合
     */
    public List<AppUserTimeline> selectAppUserTimelineByUserId(Long userId);

    /**
     * 新增APP用户履历时间线
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return 结果
     */
    public int insertAppUserTimeline(AppUserTimeline appUserTimeline);

    /**
     * 修改APP用户履历时间线
     * 
     * @param appUserTimeline APP用户履历时间线
     * @return 结果
     */
    public int updateAppUserTimeline(AppUserTimeline appUserTimeline);

    /**
     * 批量删除APP用户履历时间线
     * 
     * @param timelineIds 需要删除的APP用户履历时间线主键集合
     * @return 结果
     */
    public int deleteAppUserTimelineByTimelineIds(Long[] timelineIds);

    /**
     * 删除APP用户履历时间线信息
     * 
     * @param timelineId APP用户履历时间线主键
     * @return 结果
     */
    public int deleteAppUserTimelineByTimelineId(Long timelineId);

    /**
     * 添加用户注册事件
     * 
     * @param userId 用户ID
     * @param eventTime 事件时间
     * @return 结果
     */
    public boolean addRegisterEvent(Long userId, Date eventTime);

    /**
     * 添加任务完成事件
     * 
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param taskTitle 任务标题
     * @param amount 任务金额
     * @param eventTime 事件时间
     * @return 结果
     */
    public boolean addTaskCompleteEvent(Long userId, Long taskId, String taskTitle, java.math.BigDecimal amount, Date eventTime);

    /**
     * 添加获得奖励事件
     * 
     * @param userId 用户ID
     * @param rewardType 奖励类型
     * @param rewardName 奖励名称
     * @param eventTime 事件时间
     * @return 结果
     */
    public boolean addRewardEvent(Long userId, String rewardType, String rewardName, Date eventTime);

    /**
     * 添加等级提升事件
     * 
     * @param userId 用户ID
     * @param oldLevel 旧等级
     * @param newLevel 新等级
     * @param experience 经验值
     * @param eventTime 事件时间
     * @return 结果
     */
    public boolean addLevelUpEvent(Long userId, Integer oldLevel, Integer newLevel, Integer experience, Date eventTime);

    /**
     * 添加自定义事件
     * 
     * @param userId 用户ID
     * @param eventType 事件类型
     * @param eventTitle 事件标题
     * @param eventDesc 事件描述
     * @param eventData 事件数据
     * @param eventTime 事件时间
     * @param icon 图标
     * @param color 颜色
     * @return 结果
     */
    public boolean addCustomEvent(Long userId, String eventType, String eventTitle, String eventDesc, 
                                String eventData, Date eventTime, String icon, String color);
}
