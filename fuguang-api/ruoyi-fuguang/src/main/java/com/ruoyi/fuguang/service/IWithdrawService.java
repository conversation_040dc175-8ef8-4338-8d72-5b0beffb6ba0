package com.ruoyi.fuguang.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import com.ruoyi.fuguang.domain.WithdrawRecord;

/**
 * 提现服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IWithdrawService 
{
    /**
     * 查询提现记录
     * 
     * @param withdrawId 提现记录主键
     * @return 提现记录
     */
    public WithdrawRecord selectWithdrawRecordByWithdrawId(Long withdrawId);

    /**
     * 查询提现记录列表
     * 
     * @param withdrawRecord 提现记录
     * @return 提现记录集合
     */
    public List<WithdrawRecord> selectWithdrawRecordList(WithdrawRecord withdrawRecord);

    /**
     * 新增提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int insertWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 修改提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int updateWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 批量删除提现记录
     * 
     * @param withdrawIds 需要删除的提现记录主键集合
     * @return 结果
     */
    public int deleteWithdrawRecordByWithdrawIds(Long[] withdrawIds);

    /**
     * 删除提现记录信息
     * 
     * @param withdrawId 提现记录主键
     * @return 结果
     */
    public int deleteWithdrawRecordByWithdrawId(Long withdrawId);

    /**
     * 申请提现
     * 
     * @param userId 用户ID
     * @param withdrawAmount 提现金额
     * @param withdrawType 提现方式
     * @param payeeAccount 收款账户
     * @param payeeName 收款人姓名
     * @return 提现申请结果
     */
    public Map<String, Object> applyWithdraw(Long userId, BigDecimal withdrawAmount, String withdrawType, String payeeAccount, String payeeName);

    /**
     * 处理提现申请
     * 
     * @param withdrawId 提现记录ID
     * @param approve 是否批准
     * @param remark 备注
     * @return 处理结果
     */
    public Map<String, Object> processWithdraw(Long withdrawId, boolean approve, String remark);

    /**
     * 查询用户提现记录
     * 
     * @param userId 用户ID
     * @return 提现记录列表
     */
    public List<WithdrawRecord> selectWithdrawRecordsByUserId(Long userId);

    /**
     * 根据提现单号查询记录
     * 
     * @param withdrawNo 提现单号
     * @return 提现记录
     */
    public WithdrawRecord selectWithdrawRecordByWithdrawNo(String withdrawNo);

    /**
     * 查询提现状态
     * 
     * @param withdrawNo 提现单号
     * @return 提现状态
     */
    public String getWithdrawStatus(String withdrawNo);

    /**
     * 计算提现手续费
     *
     * @param withdrawAmount 提现金额
     * @param withdrawType 提现方式
     * @return 手续费
     */
    public BigDecimal calculateWithdrawFee(BigDecimal withdrawAmount, String withdrawType);

    /**
     * 拒绝提现申请
     *
     * @param withdrawNo 提现单号
     * @param reason 拒绝原因
     * @return 处理结果
     */
    public Map<String, Object> rejectWithdraw(String withdrawNo, String reason);
}
