package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.BalanceRecordMapper;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 余额变动记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class BalanceRecordServiceImpl implements IBalanceRecordService 
{
    @Autowired
    private BalanceRecordMapper balanceRecordMapper;

    /**
     * 查询余额变动记录
     * 
     * @param recordId 余额变动记录主键
     * @return 余额变动记录
     */
    @Override
    public BalanceRecord selectBalanceRecordByRecordId(Long recordId)
    {
        return balanceRecordMapper.selectBalanceRecordByRecordId(recordId);
    }

    /**
     * 查询余额变动记录列表
     * 
     * @param balanceRecord 余额变动记录
     * @return 余额变动记录
     */
    @Override
    public List<BalanceRecord> selectBalanceRecordList(BalanceRecord balanceRecord)
    {
        return balanceRecordMapper.selectBalanceRecordList(balanceRecord);
    }

    /**
     * 根据用户ID查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @return 余额变动记录集合
     */
    @Override
    public List<BalanceRecord> selectBalanceRecordListByUserId(Long userId)
    {
        return balanceRecordMapper.selectBalanceRecordListByUserId(userId);
    }

    /**
     * 根据用户ID和时间范围查询余额变动记录列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 余额变动记录集合
     */
    @Override
    public List<BalanceRecord> selectBalanceRecordListByUserIdAndTime(Long userId, String startTime, String endTime)
    {
        return balanceRecordMapper.selectBalanceRecordListByUserIdAndTime(userId, startTime, endTime);
    }

    /**
     * 新增余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    @Override
    public int insertBalanceRecord(BalanceRecord balanceRecord)
    {
        balanceRecord.setCreateTime(DateUtils.getNowDate());
        return balanceRecordMapper.insertBalanceRecord(balanceRecord);
    }

    /**
     * 修改余额变动记录
     * 
     * @param balanceRecord 余额变动记录
     * @return 结果
     */
    @Override
    public int updateBalanceRecord(BalanceRecord balanceRecord)
    {
        return balanceRecordMapper.updateBalanceRecord(balanceRecord);
    }

    /**
     * 批量删除余额变动记录
     * 
     * @param recordIds 需要删除的余额变动记录主键
     * @return 结果
     */
    @Override
    public int deleteBalanceRecordByRecordIds(Long[] recordIds)
    {
        return balanceRecordMapper.deleteBalanceRecordByRecordIds(recordIds);
    }

    /**
     * 删除余额变动记录信息
     * 
     * @param recordId 余额变动记录主键
     * @return 结果
     */
    @Override
    public int deleteBalanceRecordByRecordId(Long recordId)
    {
        return balanceRecordMapper.deleteBalanceRecordByRecordId(recordId);
    }
}
