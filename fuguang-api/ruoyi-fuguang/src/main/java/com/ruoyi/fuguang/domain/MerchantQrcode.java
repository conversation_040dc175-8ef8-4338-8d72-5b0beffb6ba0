package com.ruoyi.fuguang.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商家二维码信息对象 merchant_qrcode
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class MerchantQrcode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 二维码ID */
    private Long qrcodeId;

    /** 商家ID（商家申请ID） */
    @Excel(name = "商家ID")
    private Long merchantId;

    /** 商家名称 */
    @Excel(name = "商家名称")
    private String merchantName;

    /** 二维码内容 */
    @Excel(name = "二维码内容")
    private String qrcodeContent;

    /** 二维码图片URL */
    @Excel(name = "二维码图片URL")
    private String qrcodeUrl;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setQrcodeId(Long qrcodeId) 
    {
        this.qrcodeId = qrcodeId;
    }

    public Long getQrcodeId() 
    {
        return qrcodeId;
    }

    public void setMerchantId(Long merchantId) 
    {
        this.merchantId = merchantId;
    }

    public Long getMerchantId() 
    {
        return merchantId;
    }

    public void setMerchantName(String merchantName) 
    {
        this.merchantName = merchantName;
    }

    public String getMerchantName() 
    {
        return merchantName;
    }

    public void setQrcodeContent(String qrcodeContent) 
    {
        this.qrcodeContent = qrcodeContent;
    }

    public String getQrcodeContent() 
    {
        return qrcodeContent;
    }

    public void setQrcodeUrl(String qrcodeUrl) 
    {
        this.qrcodeUrl = qrcodeUrl;
    }

    public String getQrcodeUrl() 
    {
        return qrcodeUrl;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("qrcodeId", getQrcodeId())
            .append("merchantId", getMerchantId())
            .append("merchantName", getMerchantName())
            .append("qrcodeContent", getQrcodeContent())
            .append("qrcodeUrl", getQrcodeUrl())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
