package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单详情对象 mall_order_item
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public class MallOrderItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单项ID */
    private Long itemId;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long productId;

    /** 商品规格ID */
    @Excel(name = "商品规格ID")
    private Long specId;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String productName;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String specName;

    /** 商品图片 */
    @Excel(name = "商品图片")
    private String productImage;

    /** 规格图片 */
    @Excel(name = "规格图片")
    private String specImage;

    /** 商品价格 */
    @Excel(name = "商品价格")
    private BigDecimal productPrice;

    /** 购买数量 */
    @Excel(name = "购买数量")
    private Integer quantity;

    /** 小计金额 */
    @Excel(name = "小计金额")
    private BigDecimal totalPrice;

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId()
    {
        return productId;
    }
    public void setSpecId(Long specId)
    {
        this.specId = specId;
    }

    public Long getSpecId()
    {
        return specId;
    }
    public void setProductName(String productName)
    {
        this.productName = productName;
    }

    public String getProductName()
    {
        return productName;
    }
    public void setSpecName(String specName)
    {
        this.specName = specName;
    }

    public String getSpecName()
    {
        return specName;
    }
    public void setProductImage(String productImage)
    {
        this.productImage = productImage;
    }

    public String getProductImage()
    {
        return productImage;
    }
    public void setSpecImage(String specImage)
    {
        this.specImage = specImage;
    }

    public String getSpecImage()
    {
        return specImage;
    }
    public void setProductPrice(BigDecimal productPrice)
    {
        this.productPrice = productPrice;
    }

    public BigDecimal getProductPrice() 
    {
        return productPrice;
    }
    public void setQuantity(Integer quantity) 
    {
        this.quantity = quantity;
    }

    public Integer getQuantity() 
    {
        return quantity;
    }
    public void setTotalPrice(BigDecimal totalPrice) 
    {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getTotalPrice() 
    {
        return totalPrice;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("orderId", getOrderId())
            .append("productId", getProductId())
            .append("specId", getSpecId())
            .append("productName", getProductName())
            .append("specName", getSpecName())
            .append("productImage", getProductImage())
            .append("specImage", getSpecImage())
            .append("productPrice", getProductPrice())
            .append("quantity", getQuantity())
            .append("totalPrice", getTotalPrice())
            .toString();
    }
}
