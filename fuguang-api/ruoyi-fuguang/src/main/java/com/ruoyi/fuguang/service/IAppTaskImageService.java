package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.AppTaskImage;

/**
 * APP任务图片Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface IAppTaskImageService 
{
    /**
     * 查询APP任务图片
     * 
     * @param imageId APP任务图片主键
     * @return APP任务图片
     */
    public AppTaskImage selectAppTaskImageByImageId(Long imageId);

    /**
     * 查询APP任务图片列表
     * 
     * @param appTaskImage APP任务图片
     * @return APP任务图片集合
     */
    public List<AppTaskImage> selectAppTaskImageList(AppTaskImage appTaskImage);

    /**
     * 根据任务ID查询任务图片列表
     * 
     * @param taskId 任务ID
     * @return 任务图片集合
     */
    public List<AppTaskImage> selectAppTaskImagesByTaskId(Long taskId);

    /**
     * 新增APP任务图片
     * 
     * @param appTaskImage APP任务图片
     * @return 结果
     */
    public int insertAppTaskImage(AppTaskImage appTaskImage);

    /**
     * 批量新增APP任务图片
     * 
     * @param taskId 任务ID
     * @param images 图片信息列表
     * @return 结果
     */
    public int insertAppTaskImageBatch(Long taskId, List<Object> images);

    /**
     * 修改APP任务图片
     * 
     * @param appTaskImage APP任务图片
     * @return 结果
     */
    public int updateAppTaskImage(AppTaskImage appTaskImage);

    /**
     * 批量删除APP任务图片
     * 
     * @param imageIds 需要删除的APP任务图片主键集合
     * @return 结果
     */
    public int deleteAppTaskImageByImageIds(Long[] imageIds);

    /**
     * 删除APP任务图片信息
     * 
     * @param imageId APP任务图片主键
     * @return 结果
     */
    public int deleteAppTaskImageByImageId(Long imageId);

    /**
     * 根据任务ID删除任务图片
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    public int deleteAppTaskImagesByTaskId(Long taskId);
}
