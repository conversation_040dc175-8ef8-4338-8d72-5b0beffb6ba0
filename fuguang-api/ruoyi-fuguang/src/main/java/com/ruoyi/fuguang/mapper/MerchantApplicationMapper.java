package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.MerchantApplication;

/**
 * 商家申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface MerchantApplicationMapper 
{
    /**
     * 查询商家申请
     * 
     * @param applicationId 商家申请主键
     * @return 商家申请
     */
    public MerchantApplication selectMerchantApplicationByApplicationId(Long applicationId);

    /**
     * 根据用户ID查询商家申请
     * 
     * @param userId 用户ID
     * @return 商家申请
     */
    public MerchantApplication selectMerchantApplicationByUserId(Long userId);

    /**
     * 查询商家申请列表
     * 
     * @param merchantApplication 商家申请
     * @return 商家申请集合
     */
    public List<MerchantApplication> selectMerchantApplicationList(MerchantApplication merchantApplication);

    /**
     * 新增商家申请
     * 
     * @param merchantApplication 商家申请
     * @return 结果
     */
    public int insertMerchantApplication(MerchantApplication merchantApplication);

    /**
     * 修改商家申请
     * 
     * @param merchantApplication 商家申请
     * @return 结果
     */
    public int updateMerchantApplication(MerchantApplication merchantApplication);

    /**
     * 删除商家申请
     * 
     * @param applicationId 商家申请主键
     * @return 结果
     */
    public int deleteMerchantApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除商家申请
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMerchantApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 检查用户是否已有待审核或通过的申请
     * 
     * @param userId 用户ID
     * @return 申请数量
     */
    public int checkUserApplicationExists(Long userId);
}
