package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 余额变动记录对象 balance_record
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class BalanceRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String userName;

    /** 变动类型（1收入 2支出） */
    @Excel(name = "变动类型", readConverterExp = "1=收入,2=支出")
    private String changeType;

    /** 收入类型（1任务佣金 2推荐奖励 3其他收入） */
    @Excel(name = "收入类型", readConverterExp = "1=任务佣金,2=推荐奖励,3=其他收入")
    private String incomeType;

    /** 变动金额 */
    @Excel(name = "变动金额")
    private BigDecimal changeAmount;

    /** 变动前余额 */
    @Excel(name = "变动前余额")
    private BigDecimal balanceBefore;

    /** 变动后余额 */
    @Excel(name = "变动后余额")
    private BigDecimal balanceAfter;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 关联业务ID */
    @Excel(name = "关联业务ID")
    private Long businessId;

    /** 关联业务单号 */
    @Excel(name = "关联业务单号")
    private String businessNo;

    /** 变动说明 */
    @Excel(name = "变动说明")
    private String description;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setChangeType(String changeType) 
    {
        this.changeType = changeType;
    }

    public String getChangeType() 
    {
        return changeType;
    }

    public void setIncomeType(String incomeType) 
    {
        this.incomeType = incomeType;
    }

    public String getIncomeType() 
    {
        return incomeType;
    }

    public void setChangeAmount(BigDecimal changeAmount) 
    {
        this.changeAmount = changeAmount;
    }

    public BigDecimal getChangeAmount() 
    {
        return changeAmount;
    }

    public void setBalanceBefore(BigDecimal balanceBefore) 
    {
        this.balanceBefore = balanceBefore;
    }

    public BigDecimal getBalanceBefore() 
    {
        return balanceBefore;
    }

    public void setBalanceAfter(BigDecimal balanceAfter) 
    {
        this.balanceAfter = balanceAfter;
    }

    public BigDecimal getBalanceAfter() 
    {
        return balanceAfter;
    }

    public void setBusinessType(String businessType) 
    {
        this.businessType = businessType;
    }

    public String getBusinessType() 
    {
        return businessType;
    }

    public void setBusinessId(Long businessId) 
    {
        this.businessId = businessId;
    }

    public Long getBusinessId() 
    {
        return businessId;
    }

    public void setBusinessNo(String businessNo) 
    {
        this.businessNo = businessNo;
    }

    public String getBusinessNo() 
    {
        return businessNo;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("changeType", getChangeType())
            .append("incomeType", getIncomeType())
            .append("changeAmount", getChangeAmount())
            .append("balanceBefore", getBalanceBefore())
            .append("balanceAfter", getBalanceAfter())
            .append("businessType", getBusinessType())
            .append("businessId", getBusinessId())
            .append("businessNo", getBusinessNo())
            .append("description", getDescription())
            .append("createTime", getCreateTime())
            .toString();
    }
}
