package com.ruoyi.fuguang.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP功能配置对象 app_function
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public class AppFunction extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 功能ID */
    private Long functionId;

    /** 功能名称 */
    @Excel(name = "功能名称")
    private String functionName;

    /** 功能图标 */
    @Excel(name = "功能图标")
    private String functionIcon;

    /** 功能链接 */
    @Excel(name = "功能链接")
    private String functionUrl;

    /** 功能类型（0内部页面 1外部链接） */
    @Excel(name = "功能类型", readConverterExp = "0=内部页面,1=外部链接")
    private String functionType;

    /** 显示位置（0首页 1我的页面） */
    @Excel(name = "显示位置", readConverterExp = "0=首页,1=我的页面")
    private String displayLocation;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setFunctionId(Long functionId) 
    {
        this.functionId = functionId;
    }

    public Long getFunctionId() 
    {
        return functionId;
    }

    public void setFunctionName(String functionName) 
    {
        this.functionName = functionName;
    }

    public String getFunctionName() 
    {
        return functionName;
    }

    public void setFunctionIcon(String functionIcon) 
    {
        this.functionIcon = functionIcon;
    }

    public String getFunctionIcon() 
    {
        return functionIcon;
    }

    public void setFunctionUrl(String functionUrl) 
    {
        this.functionUrl = functionUrl;
    }

    public String getFunctionUrl() 
    {
        return functionUrl;
    }

    public void setFunctionType(String functionType) 
    {
        this.functionType = functionType;
    }

    public String getFunctionType()
    {
        return functionType;
    }

    public void setDisplayLocation(String displayLocation)
    {
        this.displayLocation = displayLocation;
    }

    public String getDisplayLocation()
    {
        return displayLocation;
    }

    public void setSortOrder(Integer sortOrder)
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("functionId", getFunctionId())
            .append("functionName", getFunctionName())
            .append("functionIcon", getFunctionIcon())
            .append("functionUrl", getFunctionUrl())
            .append("functionType", getFunctionType())
            .append("displayLocation", getDisplayLocation())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
