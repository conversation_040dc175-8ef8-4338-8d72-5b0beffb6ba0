package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppUserProfileMapper;
import com.ruoyi.fuguang.domain.AppUserProfile;
import com.ruoyi.fuguang.service.IAppUserProfileService;

/**
 * APP用户个人简介Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Service
public class AppUserProfileServiceImpl implements IAppUserProfileService 
{
    @Autowired
    private AppUserProfileMapper appUserProfileMapper;

    /**
     * 查询APP用户个人简介
     * 
     * @param profileId APP用户个人简介主键
     * @return APP用户个人简介
     */
    @Override
    public AppUserProfile selectAppUserProfileByProfileId(Long profileId)
    {
        return appUserProfileMapper.selectAppUserProfileByProfileId(profileId);
    }

    /**
     * 根据用户ID查询APP用户个人简介
     * 
     * @param userId 用户ID
     * @return APP用户个人简介
     */
    @Override
    public AppUserProfile selectAppUserProfileByUserId(Long userId)
    {
        return appUserProfileMapper.selectAppUserProfileByUserId(userId);
    }

    /**
     * 查询APP用户个人简介列表
     * 
     * @param appUserProfile APP用户个人简介
     * @return APP用户个人简介
     */
    @Override
    public List<AppUserProfile> selectAppUserProfileList(AppUserProfile appUserProfile)
    {
        return appUserProfileMapper.selectAppUserProfileList(appUserProfile);
    }

    /**
     * 新增APP用户个人简介
     * 
     * @param appUserProfile APP用户个人简介
     * @return 结果
     */
    @Override
    public int insertAppUserProfile(AppUserProfile appUserProfile)
    {
        appUserProfile.setCreateTime(DateUtils.getNowDate());
        return appUserProfileMapper.insertAppUserProfile(appUserProfile);
    }

    /**
     * 修改APP用户个人简介
     * 
     * @param appUserProfile APP用户个人简介
     * @return 结果
     */
    @Override
    public int updateAppUserProfile(AppUserProfile appUserProfile)
    {
        appUserProfile.setUpdateTime(DateUtils.getNowDate());
        return appUserProfileMapper.updateAppUserProfile(appUserProfile);
    }

    /**
     * 批量删除APP用户个人简介
     * 
     * @param profileIds 需要删除的APP用户个人简介主键
     * @return 结果
     */
    @Override
    public int deleteAppUserProfileByProfileIds(Long[] profileIds)
    {
        return appUserProfileMapper.deleteAppUserProfileByProfileIds(profileIds);
    }

    /**
     * 删除APP用户个人简介信息
     * 
     * @param profileId APP用户个人简介主键
     * @return 结果
     */
    @Override
    public int deleteAppUserProfileByProfileId(Long profileId)
    {
        return appUserProfileMapper.deleteAppUserProfileByProfileId(profileId);
    }

    /**
     * 初始化用户个人简介
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean initUserProfile(Long userId)
    {
        // 检查是否已存在
        AppUserProfile existProfile = appUserProfileMapper.selectAppUserProfileByUserId(userId);
        if (existProfile != null) {
            return true;
        }

        // 创建默认个人简介
        AppUserProfile profile = new AppUserProfile();
        profile.setUserId(userId);
        profile.setCreditScore(100); // 默认信用分100
        profile.setTaskScore(0);
        profile.setPovertyReliefBadge("0");
        profile.setProfileDesc("");
        profile.setProfileImages("[]");
        profile.setProfileVideo("");
        profile.setTotalTasks(0);
        profile.setCompletedTasks(0);
        profile.setSuccessRate(BigDecimal.ZERO);
        profile.setTotalEarnings(BigDecimal.ZERO);
        profile.setLevel(1);
        profile.setExperience(0);
        profile.setStatus("0");
        profile.setCreateBy("system");
        profile.setCreateTime(new Date());

        return insertAppUserProfile(profile) > 0;
    }

    /**
     * 更新用户信用分
     * 
     * @param userId 用户ID
     * @param creditScore 信用分变化值
     * @return 结果
     */
    @Override
    public boolean updateCreditScore(Long userId, Integer creditScore)
    {
        return appUserProfileMapper.updateCreditScore(userId, creditScore) > 0;
    }

    /**
     * 更新用户任务分
     * 
     * @param userId 用户ID
     * @param taskScore 任务分变化值
     * @return 结果
     */
    @Override
    public boolean updateTaskScore(Long userId, Integer taskScore)
    {
        return appUserProfileMapper.updateTaskScore(userId, taskScore) > 0;
    }

    /**
     * 更新用户任务统计
     * 
     * @param userId 用户ID
     * @param totalTasks 总任务数变化值
     * @param completedTasks 完成任务数变化值
     * @param earnings 收益金额
     * @return 结果
     */
    @Override
    public boolean updateTaskStats(Long userId, Integer totalTasks, Integer completedTasks, BigDecimal earnings)
    {
        return appUserProfileMapper.updateTaskStats(userId, totalTasks, completedTasks, earnings) > 0;
    }

    /**
     * 更新用户等级和经验
     * 
     * @param userId 用户ID
     * @param experience 经验值增加量
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateExperience(Long userId, Integer experience)
    {
        // 获取当前用户简介
        AppUserProfile profile = selectAppUserProfileByUserId(userId);
        if (profile == null) {
            return false;
        }

        Integer currentExp = profile.getExperience() + experience;
        Integer currentLevel = profile.getLevel();
        Integer newLevel = calculateLevel(currentExp);

        return appUserProfileMapper.updateLevelAndExperience(userId, newLevel, currentExp) > 0;
    }

    /**
     * 设置扶贫救援徽章
     * 
     * @param userId 用户ID
     * @param povertyReliefBadge 扶贫救援标志
     * @return 结果
     */
    @Override
    public boolean updatePovertyReliefBadge(Long userId, String povertyReliefBadge)
    {
        return appUserProfileMapper.updatePovertyReliefBadge(userId, povertyReliefBadge) > 0;
    }

    /**
     * 计算用户等级
     * 
     * @param experience 经验值
     * @return 等级
     */
    @Override
    public Integer calculateLevel(Integer experience)
    {
        if (experience < 100) return 1;
        if (experience < 300) return 2;
        if (experience < 600) return 3;
        if (experience < 1000) return 4;
        if (experience < 1500) return 5;
        if (experience < 2100) return 6;
        if (experience < 2800) return 7;
        if (experience < 3600) return 8;
        if (experience < 4500) return 9;
        return 10; // 最高等级
    }
}
