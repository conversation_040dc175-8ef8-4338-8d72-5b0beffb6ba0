package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.mapper.WithdrawRecordMapper;
import com.ruoyi.fuguang.service.IAlipayService;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.fuguang.service.IUserBalanceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.domain.WithdrawRecord;
import com.ruoyi.fuguang.service.IWithdrawService;

/**
 * 提现服务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class WithdrawServiceImpl implements IWithdrawService 
{
    private static final Logger log = LoggerFactory.getLogger(WithdrawServiceImpl.class);

    @Autowired
    private WithdrawRecordMapper withdrawRecordMapper;

    @Autowired
    private IAlipayService alipayService;

    @Autowired
    private IAppUserService appUserService;

    @Autowired
    private ICommissionBillService commissionBillService;

    @Autowired
    private IUserBalanceService userBalanceService;

    /**
     * 查询提现记录
     * 
     * @param withdrawId 提现记录主键
     * @return 提现记录
     */
    @Override
    public WithdrawRecord selectWithdrawRecordByWithdrawId(Long withdrawId)
    {
        return withdrawRecordMapper.selectWithdrawRecordByWithdrawId(withdrawId);
    }

    /**
     * 查询提现记录列表
     * 
     * @param withdrawRecord 提现记录
     * @return 提现记录
     */
    @Override
    public List<WithdrawRecord> selectWithdrawRecordList(WithdrawRecord withdrawRecord)
    {
        return withdrawRecordMapper.selectWithdrawRecordList(withdrawRecord);
    }

    /**
     * 新增提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    @Override
    public int insertWithdrawRecord(WithdrawRecord withdrawRecord)
    {
        withdrawRecord.setCreateTime(DateUtils.getNowDate());
        return withdrawRecordMapper.insertWithdrawRecord(withdrawRecord);
    }

    /**
     * 修改提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    @Override
    public int updateWithdrawRecord(WithdrawRecord withdrawRecord)
    {
        withdrawRecord.setUpdateTime(DateUtils.getNowDate());
        return withdrawRecordMapper.updateWithdrawRecord(withdrawRecord);
    }

    /**
     * 批量删除提现记录
     * 
     * @param withdrawIds 需要删除的提现记录主键
     * @return 结果
     */
    @Override
    public int deleteWithdrawRecordByWithdrawIds(Long[] withdrawIds)
    {
        return withdrawRecordMapper.deleteWithdrawRecordByWithdrawIds(withdrawIds);
    }

    /**
     * 删除提现记录信息
     * 
     * @param withdrawId 提现记录主键
     * @return 结果
     */
    @Override
    public int deleteWithdrawRecordByWithdrawId(Long withdrawId)
    {
        return withdrawRecordMapper.deleteWithdrawRecordByWithdrawId(withdrawId);
    }

    /**
     * 申请提现
     * 
     * @param userId 用户ID
     * @param withdrawAmount 提现金额
     * @param withdrawType 提现方式
     * @param payeeAccount 收款账户
     * @param payeeName 收款人姓名
     * @return 提现申请结果
     */
    @Override
    @Transactional
    public Map<String, Object> applyWithdraw(Long userId, BigDecimal withdrawAmount, String withdrawType, String payeeAccount, String payeeName)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取用户信息
            AppUser user = appUserService.selectAppUserByUserId(userId);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 验证提现金额
            if (withdrawAmount.compareTo(BigDecimal.ZERO) <= 0) {
                result.put("success", false);
                result.put("message", "提现金额必须大于0");
                return result;
            }

            // 验证最小提现金额
            BigDecimal minAmount = new BigDecimal("10.00");
            if (withdrawAmount.compareTo(minAmount) < 0) {
                result.put("success", false);
                result.put("message", "最小提现金额为" + minAmount + "元");
                return result;
            }

            // 计算手续费
            BigDecimal withdrawFee = calculateWithdrawFee(withdrawAmount, withdrawType);
            BigDecimal actualAmount = withdrawAmount.subtract(withdrawFee);

            // 生成提现单号
            String withdrawNo = "WD_" + System.currentTimeMillis() + "_" + userId;

            // 创建提现记录
            WithdrawRecord withdrawRecord = new WithdrawRecord();
            withdrawRecord.setUserId(userId);
            withdrawRecord.setUserName(user.getNickName());
            withdrawRecord.setWithdrawNo(withdrawNo);
            withdrawRecord.setWithdrawAmount(withdrawAmount);
            withdrawRecord.setWithdrawFee(withdrawFee);
            withdrawRecord.setActualAmount(actualAmount);
            withdrawRecord.setWithdrawType(withdrawType);
            withdrawRecord.setWithdrawStatus("0"); // 申请中
            withdrawRecord.setPayeeAccount(payeeAccount);
            withdrawRecord.setPayeeName(payeeName);
            withdrawRecord.setApplyTime(new Date());

            int insertResult = insertWithdrawRecord(withdrawRecord);
            
            if (insertResult > 0) {
                result.put("success", true);
                result.put("message", "提现申请提交成功");
                result.put("withdrawNo", withdrawNo);
                result.put("withdrawAmount", withdrawAmount);
                result.put("withdrawFee", withdrawFee);
                result.put("actualAmount", actualAmount);
                log.info("用户{}申请提现成功，提现单号：{}，金额：{}", userId, withdrawNo, withdrawAmount);
            } else {
                result.put("success", false);
                result.put("message", "提现申请提交失败");
            }

        } catch (Exception e) {
            log.error("申请提现异常，用户ID：{}，金额：{}", userId, withdrawAmount, e);
            result.put("success", false);
            result.put("message", "申请提现异常：" + e.getMessage());
        }

        return result;
    }

    /**
     * 处理提现申请
     * 
     * @param withdrawId 提现记录ID
     * @param approve 是否批准
     * @param remark 备注
     * @return 处理结果
     */
    @Override
    @Transactional
    public Map<String, Object> processWithdraw(Long withdrawId, boolean approve, String remark)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WithdrawRecord withdrawRecord = selectWithdrawRecordByWithdrawId(withdrawId);
            if (withdrawRecord == null) {
                result.put("success", false);
                result.put("message", "提现记录不存在");
                return result;
            }

            if (!"0".equals(withdrawRecord.getWithdrawStatus())) {
                result.put("success", false);
                result.put("message", "提现记录状态不正确");
                return result;
            }

            if (approve) {
                // 批准提现，调用支付宝转账接口
                if ("1".equals(withdrawRecord.getWithdrawType())) {
                    // 支付宝提现
                    Map<String, Object> transferResult = alipayService.transfer(
                        withdrawRecord.getWithdrawNo(),
                        withdrawRecord.getPayeeAccount(),
                        withdrawRecord.getActualAmount(),
                        withdrawRecord.getPayeeName(),
                        "用户提现"
                    );

                    if ((Boolean) transferResult.get("success")) {
                        // 转账成功
                        withdrawRecord.setWithdrawStatus("2"); // 提现成功
                        withdrawRecord.setTradeNo((String) transferResult.get("payFundOrderId"));
                        withdrawRecord.setProcessTime(new Date());
                        withdrawRecord.setFinishTime(new Date());

                        // 更新佣金账单中的提现记录
                        commissionBillService.updateWithdrawAmount(
                            withdrawRecord.getUserId(),
                            withdrawRecord.getUserName(),
                            withdrawRecord.getWithdrawAmount()
                        );

                        result.put("success", true);
                        result.put("message", "提现处理成功");
                        log.info("提现处理成功，提现单号：{}", withdrawRecord.getWithdrawNo());
                    } else {
                        // 转账失败
                        withdrawRecord.setWithdrawStatus("3"); // 提现失败
                        withdrawRecord.setFailReason((String) transferResult.get("errorMsg"));
                        withdrawRecord.setProcessTime(new Date());
                        withdrawRecord.setFinishTime(new Date());
                        
                        result.put("success", false);
                        result.put("message", "提现转账失败：" + transferResult.get("errorMsg"));
                        log.error("提现转账失败，提现单号：{}，错误：{}", withdrawRecord.getWithdrawNo(), transferResult.get("errorMsg"));
                    }
                } else {
                    result.put("success", false);
                    result.put("message", "暂不支持该提现方式");
                    return result;
                }
            } else {
                // 拒绝提现
                withdrawRecord.setWithdrawStatus("3"); // 提现失败
                withdrawRecord.setFailReason(remark);
                withdrawRecord.setProcessTime(new Date());
                withdrawRecord.setFinishTime(new Date());
                
                result.put("success", true);
                result.put("message", "提现申请已拒绝");
                log.info("提现申请已拒绝，提现单号：{}，原因：{}", withdrawRecord.getWithdrawNo(), remark);
            }

            updateWithdrawRecord(withdrawRecord);

        } catch (Exception e) {
            log.error("处理提现申请异常，提现记录ID：{}", withdrawId, e);
            result.put("success", false);
            result.put("message", "处理提现申请异常：" + e.getMessage());
        }

        return result;
    }

    /**
     * 查询用户提现记录
     * 
     * @param userId 用户ID
     * @return 提现记录列表
     */
    @Override
    public List<WithdrawRecord> selectWithdrawRecordsByUserId(Long userId)
    {
        return withdrawRecordMapper.selectWithdrawRecordsByUserId(userId);
    }

    /**
     * 根据提现单号查询记录
     * 
     * @param withdrawNo 提现单号
     * @return 提现记录
     */
    @Override
    public WithdrawRecord selectWithdrawRecordByWithdrawNo(String withdrawNo)
    {
        return withdrawRecordMapper.selectWithdrawRecordByWithdrawNo(withdrawNo);
    }

    /**
     * 查询提现状态
     * 
     * @param withdrawNo 提现单号
     * @return 提现状态
     */
    @Override
    public String getWithdrawStatus(String withdrawNo)
    {
        WithdrawRecord record = selectWithdrawRecordByWithdrawNo(withdrawNo);
        return record != null ? record.getWithdrawStatus() : "0";
    }



    /**
     * 计算提现手续费
     *
     * @param withdrawAmount 提现金额
     * @param withdrawType 提现方式
     * @return 手续费
     */
    @Override
    public BigDecimal calculateWithdrawFee(BigDecimal withdrawAmount, String withdrawType)
    {
        // 根据提现方式计算手续费
        BigDecimal feeRate;
        switch (withdrawType) {
            case "1": // 支付宝
                feeRate = new BigDecimal("0.006"); // 0.6%
                break;
            case "2": // 微信
                feeRate = new BigDecimal("0.006"); // 0.6%
                break;
            case "3": // 银行卡
                feeRate = new BigDecimal("0.01"); // 1%
                break;
            default:
                feeRate = new BigDecimal("0.006");
                break;
        }

        BigDecimal fee = withdrawAmount.multiply(feeRate);
        // 最低手续费1元
        BigDecimal minFee = new BigDecimal("1.00");
        return fee.compareTo(minFee) < 0 ? minFee : fee;
    }

    /**
     * 拒绝提现申请
     *
     * @param withdrawNo 提现单号
     * @param reason 拒绝原因
     * @return 处理结果
     */
    @Override
    @Transactional
    public Map<String, Object> rejectWithdraw(String withdrawNo, String reason)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询提现记录
            WithdrawRecord withdrawRecord = withdrawRecordMapper.selectWithdrawRecordByWithdrawNo(withdrawNo);
            if (withdrawRecord == null) {
                result.put("success", false);
                result.put("message", "提现记录不存在");
                return result;
            }

            // 检查提现状态
            if (!"0".equals(withdrawRecord.getWithdrawStatus())) {
                result.put("success", false);
                result.put("message", "只能拒绝申请中的提现记录");
                return result;
            }

            // 更新提现记录状态为失败
            withdrawRecord.setWithdrawStatus("3"); // 提现失败
            withdrawRecord.setFailReason(reason != null ? reason : "管理员拒绝");
            withdrawRecord.setProcessTime(new Date());
            withdrawRecord.setFinishTime(new Date());
            withdrawRecord.setUpdateTime(new Date());

            int updateResult = withdrawRecordMapper.updateWithdrawRecord(withdrawRecord);
            if (updateResult <= 0) {
                result.put("success", false);
                result.put("message", "更新提现记录失败");
                return result;
            }

            // 将提现金额返回到用户余额
            boolean balanceResult = userBalanceService.increaseBalance(
                withdrawRecord.getUserId(),
                withdrawRecord.getWithdrawAmount(),
                "withdraw_reject",
                withdrawRecord.getWithdrawId(),
                withdrawRecord.getWithdrawNo(),
                "提现被拒绝，返回余额：" + (reason != null ? reason : "管理员拒绝"),
                null // 这不是收入，所以不设置收入类型
            );

            if (!balanceResult) {
                // 如果余额返回失败，需要回滚提现记录状态
                withdrawRecord.setWithdrawStatus("0"); // 恢复为申请中
                withdrawRecord.setFailReason(null);
                withdrawRecord.setProcessTime(null);
                withdrawRecord.setFinishTime(null);
                withdrawRecordMapper.updateWithdrawRecord(withdrawRecord);

                result.put("success", false);
                result.put("message", "返回用户余额失败");
                return result;
            }

            result.put("success", true);
            result.put("message", "提现申请已拒绝，金额已返回用户余额");
            log.info("提现申请拒绝成功，提现单号：{}，用户ID：{}，金额：{}",
                    withdrawNo, withdrawRecord.getUserId(), withdrawRecord.getWithdrawAmount());

        } catch (Exception e) {
            log.error("拒绝提现申请异常，提现单号：{}", withdrawNo, e);
            result.put("success", false);
            result.put("message", "拒绝提现申请时发生异常：" + e.getMessage());
        }

        return result;
    }
}
