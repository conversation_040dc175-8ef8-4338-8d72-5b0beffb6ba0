package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.MallProductSpec;

/**
 * 商品规格Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IMallProductSpecService 
{
    /**
     * 查询商品规格
     * 
     * @param specId 商品规格主键
     * @return 商品规格
     */
    public MallProductSpec selectMallProductSpecBySpecId(Long specId);

    /**
     * 查询商品规格列表
     * 
     * @param mallProductSpec 商品规格
     * @return 商品规格集合
     */
    public List<MallProductSpec> selectMallProductSpecList(MallProductSpec mallProductSpec);

    /**
     * 根据商品ID查询规格列表
     * 
     * @param productId 商品ID
     * @return 商品规格集合
     */
    public List<MallProductSpec> selectMallProductSpecByProductId(Long productId);

    /**
     * 新增商品规格
     * 
     * @param mallProductSpec 商品规格
     * @return 结果
     */
    public int insertMallProductSpec(MallProductSpec mallProductSpec);

    /**
     * 修改商品规格
     * 
     * @param mallProductSpec 商品规格
     * @return 结果
     */
    public int updateMallProductSpec(MallProductSpec mallProductSpec);

    /**
     * 批量删除商品规格
     * 
     * @param specIds 需要删除的商品规格主键集合
     * @return 结果
     */
    public int deleteMallProductSpecBySpecIds(Long[] specIds);

    /**
     * 删除商品规格信息
     * 
     * @param specId 商品规格主键
     * @return 结果
     */
    public int deleteMallProductSpecBySpecId(Long specId);

    /**
     * 根据商品ID删除规格
     * 
     * @param productId 商品ID
     * @return 结果
     */
    public int deleteMallProductSpecByProductId(Long productId);
}
