package com.ruoyi.fuguang.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.MallCategoryMapper;
import com.ruoyi.fuguang.mapper.MallProductMapper;
import com.ruoyi.fuguang.domain.MallCategory;
import com.ruoyi.fuguang.domain.MallProduct;
import com.ruoyi.fuguang.service.IMallCategoryService;

/**
 * 商品分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MallCategoryServiceImpl implements IMallCategoryService 
{
    @Autowired
    private MallCategoryMapper mallCategoryMapper;

    @Autowired
    private MallProductMapper mallProductMapper;

    /**
     * 查询商品分类
     * 
     * @param categoryId 商品分类主键
     * @return 商品分类
     */
    @Override
    public MallCategory selectMallCategoryByCategoryId(Long categoryId)
    {
        return mallCategoryMapper.selectMallCategoryByCategoryId(categoryId);
    }

    /**
     * 查询商品分类列表
     * 
     * @param mallCategory 商品分类
     * @return 商品分类
     */
    @Override
    public List<MallCategory> selectMallCategoryList(MallCategory mallCategory)
    {
        return mallCategoryMapper.selectMallCategoryList(mallCategory);
    }

    /**
     * 新增商品分类
     * 
     * @param mallCategory 商品分类
     * @return 结果
     */
    @Override
    public int insertMallCategory(MallCategory mallCategory)
    {
        mallCategory.setCreateTime(DateUtils.getNowDate());
        return mallCategoryMapper.insertMallCategory(mallCategory);
    }

    /**
     * 修改商品分类
     * 
     * @param mallCategory 商品分类
     * @return 结果
     */
    @Override
    public int updateMallCategory(MallCategory mallCategory)
    {
        mallCategory.setUpdateTime(DateUtils.getNowDate());
        return mallCategoryMapper.updateMallCategory(mallCategory);
    }

    /**
     * 批量删除商品分类
     * 
     * @param categoryIds 需要删除的商品分类主键
     * @return 结果
     */
    @Override
    public int deleteMallCategoryByCategoryIds(Long[] categoryIds)
    {
        return mallCategoryMapper.deleteMallCategoryByCategoryIds(categoryIds);
    }

    /**
     * 删除商品分类信息
     * 
     * @param categoryId 商品分类主键
     * @return 结果
     */
    @Override
    public int deleteMallCategoryByCategoryId(Long categoryId)
    {
        return mallCategoryMapper.deleteMallCategoryByCategoryId(categoryId);
    }

    /**
     * 查询所有启用的分类（树形结构）
     * 
     * @return 分类列表
     */
    @Override
    public List<MallCategory> selectEnabledCategoryTree()
    {
        return mallCategoryMapper.selectEnabledCategoryTree();
    }

    /**
     * 查询一级分类列表
     * 
     * @return 一级分类列表
     */
    @Override
    public List<MallCategory> selectTopCategoryList()
    {
        return mallCategoryMapper.selectTopCategoryList();
    }

    /**
     * 校验分类名称是否唯一
     * 
     * @param mallCategory 分类信息
     * @return 结果
     */
    @Override
    public String checkCategoryNameUnique(MallCategory mallCategory)
    {
        Long categoryId = StringUtils.isNull(mallCategory.getCategoryId()) ? -1L : mallCategory.getCategoryId();
        MallCategory info = new MallCategory();
        info.setCategoryName(mallCategory.getCategoryName());
        info.setParentId(mallCategory.getParentId());
        List<MallCategory> list = mallCategoryMapper.selectMallCategoryList(info);
        if (StringUtils.isNotEmpty(list) && list.get(0).getCategoryId().longValue() != categoryId.longValue())
        {
            return "1"; // 不唯一
        }
        return "0"; // 唯一
    }

    /**
     * 校验是否存在子分类
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    @Override
    public boolean hasChildByCategory(Long categoryId)
    {
        int result = mallCategoryMapper.selectChildrenCategoryCount(categoryId);
        return result > 0;
    }

    /**
     * 校验分类是否存在商品
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    @Override
    public boolean checkCategoryExistProduct(Long categoryId)
    {
        MallProduct product = new MallProduct();
        product.setCategoryId(categoryId);
        List<MallProduct> list = mallProductMapper.selectMallProductList(product);
        return StringUtils.isNotEmpty(list);
    }
}
