package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppTaskType;

/**
 * APP任务类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface AppTaskTypeMapper 
{
    /**
     * 查询APP任务类型
     * 
     * @param typeId APP任务类型主键
     * @return APP任务类型
     */
    public AppTaskType selectAppTaskTypeByTypeId(Long typeId);

    /**
     * 查询APP任务类型列表
     * 
     * @param appTaskType APP任务类型
     * @return APP任务类型集合
     */
    public List<AppTaskType> selectAppTaskTypeList(AppTaskType appTaskType);

    /**
     * 新增APP任务类型
     * 
     * @param appTaskType APP任务类型
     * @return 结果
     */
    public int insertAppTaskType(AppTaskType appTaskType);

    /**
     * 修改APP任务类型
     * 
     * @param appTaskType APP任务类型
     * @return 结果
     */
    public int updateAppTaskType(AppTaskType appTaskType);

    /**
     * 删除APP任务类型
     * 
     * @param typeId APP任务类型主键
     * @return 结果
     */
    public int deleteAppTaskTypeByTypeId(Long typeId);

    /**
     * 批量删除APP任务类型
     * 
     * @param typeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppTaskTypeByTypeIds(Long[] typeIds);

    /**
     * 查询一级任务类型列表
     * 
     * @return 一级任务类型集合
     */
    public List<AppTaskType> selectFirstLevelTaskTypes();

    /**
     * 根据父类型ID查询子类型列表
     * 
     * @param parentId 父类型ID
     * @return 子类型集合
     */
    public List<AppTaskType> selectTaskTypesByParentId(Long parentId);
}
