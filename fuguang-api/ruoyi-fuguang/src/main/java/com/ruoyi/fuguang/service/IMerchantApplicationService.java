package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.MerchantApplication;

/**
 * 商家申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IMerchantApplicationService 
{
    /**
     * 查询商家申请
     * 
     * @param applicationId 商家申请主键
     * @return 商家申请
     */
    public MerchantApplication selectMerchantApplicationByApplicationId(Long applicationId);

    /**
     * 根据用户ID查询商家申请
     * 
     * @param userId 用户ID
     * @return 商家申请
     */
    public MerchantApplication selectMerchantApplicationByUserId(Long userId);

    /**
     * 查询商家申请列表
     * 
     * @param merchantApplication 商家申请
     * @return 商家申请集合
     */
    public List<MerchantApplication> selectMerchantApplicationList(MerchantApplication merchantApplication);

    /**
     * 新增商家申请
     * 
     * @param merchantApplication 商家申请
     * @return 结果
     */
    public int insertMerchantApplication(MerchantApplication merchantApplication);

    /**
     * 修改商家申请
     * 
     * @param merchantApplication 商家申请
     * @return 结果
     */
    public int updateMerchantApplication(MerchantApplication merchantApplication);

    /**
     * 批量删除商家申请
     * 
     * @param applicationIds 需要删除的商家申请主键集合
     * @return 结果
     */
    public int deleteMerchantApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除商家申请信息
     * 
     * @param applicationId 商家申请主键
     * @return 结果
     */
    public int deleteMerchantApplicationByApplicationId(Long applicationId);

    /**
     * 检查用户是否可以申请成为商家
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkUserCanApply(Long userId);

    /**
     * 提交商家申请
     * 
     * @param merchantApplication 商家申请信息
     * @return 结果
     */
    public boolean submitApplication(MerchantApplication merchantApplication);

    /**
     * 审核商家申请
     * 
     * @param applicationId 申请ID
     * @param applicationStatus 审核状态（1通过 2拒绝）
     * @param auditRemark 审核备注
     * @param auditBy 审核人
     * @return 结果
     */
    public boolean auditApplication(Long applicationId, String applicationStatus, String auditRemark, String auditBy);

    /**
     * 校验申请数据
     * 
     * @param merchantApplication 申请数据
     * @return 校验结果
     */
    public String validateApplicationData(MerchantApplication merchantApplication);
}
