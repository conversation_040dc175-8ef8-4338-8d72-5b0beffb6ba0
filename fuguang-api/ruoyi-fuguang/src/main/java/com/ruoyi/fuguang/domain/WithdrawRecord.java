package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 提现记录对象 withdraw_record
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class WithdrawRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 提现记录ID */
    private Long withdrawId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String userName;

    /** 提现单号 */
    @Excel(name = "提现单号")
    private String withdrawNo;

    /** 提现金额 */
    @Excel(name = "提现金额")
    private BigDecimal withdrawAmount;

    /** 提现手续费 */
    @Excel(name = "提现手续费")
    private BigDecimal withdrawFee;

    /** 实际到账金额 */
    @Excel(name = "实际到账金额")
    private BigDecimal actualAmount;

    /** 提现方式（1支付宝 2微信 3银行卡） */
    @Excel(name = "提现方式", readConverterExp = "1=支付宝,2=微信,3=银行卡")
    private String withdrawType;

    /** 提现状态（0申请中 1处理中 2提现成功 3提现失败） */
    @Excel(name = "提现状态", readConverterExp = "0=申请中,1=处理中,2=提现成功,3=提现失败")
    private String withdrawStatus;

    /** 收款账户 */
    @Excel(name = "收款账户")
    private String payeeAccount;

    /** 收款人姓名 */
    @Excel(name = "收款人姓名")
    private String payeeName;

    /** 第三方交易号 */
    @Excel(name = "第三方交易号")
    private String tradeNo;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String failReason;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    public void setWithdrawId(Long withdrawId) 
    {
        this.withdrawId = withdrawId;
    }

    public Long getWithdrawId() 
    {
        return withdrawId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setWithdrawNo(String withdrawNo) 
    {
        this.withdrawNo = withdrawNo;
    }

    public String getWithdrawNo() 
    {
        return withdrawNo;
    }

    public void setWithdrawAmount(BigDecimal withdrawAmount) 
    {
        this.withdrawAmount = withdrawAmount;
    }

    public BigDecimal getWithdrawAmount() 
    {
        return withdrawAmount;
    }

    public void setWithdrawFee(BigDecimal withdrawFee) 
    {
        this.withdrawFee = withdrawFee;
    }

    public BigDecimal getWithdrawFee() 
    {
        return withdrawFee;
    }

    public void setActualAmount(BigDecimal actualAmount) 
    {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getActualAmount() 
    {
        return actualAmount;
    }

    public void setWithdrawType(String withdrawType) 
    {
        this.withdrawType = withdrawType;
    }

    public String getWithdrawType()
    {
        return withdrawType;
    }

    /**
     * 获取提现渠道名称
     *
     * @return 提现渠道名称
     */
    @JsonProperty("withdrawTypeName")
    public String getWithdrawTypeName()
    {
        if (withdrawType == null) {
            return "";
        }
        switch (withdrawType) {
            case "1":
                return "支付宝";
            case "2":
                return "微信";
            case "3":
                return "银行卡";
            default:
                return "未知";
        }
    }

    public void setWithdrawStatus(String withdrawStatus) 
    {
        this.withdrawStatus = withdrawStatus;
    }

    public String getWithdrawStatus()
    {
        return withdrawStatus;
    }

    /**
     * 获取提现状态名称
     *
     * @return 提现状态名称
     */
    @JsonProperty("withdrawStatusName")
    public String getWithdrawStatusName()
    {
        if (withdrawStatus == null) {
            return "";
        }
        switch (withdrawStatus) {
            case "0":
                return "申请中";
            case "1":
                return "处理中";
            case "2":
                return "提现成功";
            case "3":
                return "提现失败";
            default:
                return "未知";
        }
    }

    public void setPayeeAccount(String payeeAccount) 
    {
        this.payeeAccount = payeeAccount;
    }

    public String getPayeeAccount() 
    {
        return payeeAccount;
    }

    public void setPayeeName(String payeeName) 
    {
        this.payeeName = payeeName;
    }

    public String getPayeeName() 
    {
        return payeeName;
    }

    public void setTradeNo(String tradeNo) 
    {
        this.tradeNo = tradeNo;
    }

    public String getTradeNo() 
    {
        return tradeNo;
    }

    public void setFailReason(String failReason) 
    {
        this.failReason = failReason;
    }

    public String getFailReason() 
    {
        return failReason;
    }

    public void setApplyTime(Date applyTime) 
    {
        this.applyTime = applyTime;
    }

    public Date getApplyTime() 
    {
        return applyTime;
    }

    public void setProcessTime(Date processTime) 
    {
        this.processTime = processTime;
    }

    public Date getProcessTime() 
    {
        return processTime;
    }

    public void setFinishTime(Date finishTime) 
    {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() 
    {
        return finishTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("withdrawId", getWithdrawId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("withdrawNo", getWithdrawNo())
            .append("withdrawAmount", getWithdrawAmount())
            .append("withdrawFee", getWithdrawFee())
            .append("actualAmount", getActualAmount())
            .append("withdrawType", getWithdrawType())
            .append("withdrawStatus", getWithdrawStatus())
            .append("payeeAccount", getPayeeAccount())
            .append("payeeName", getPayeeName())
            .append("tradeNo", getTradeNo())
            .append("failReason", getFailReason())
            .append("applyTime", getApplyTime())
            .append("processTime", getProcessTime())
            .append("finishTime", getFinishTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
