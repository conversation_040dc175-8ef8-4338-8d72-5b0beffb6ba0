package com.ruoyi.common.utils;

import java.util.Random;
import java.util.regex.Pattern;

/**
 * 手机号相关工具类
 * 
 * <AUTHOR>
 */
public class PhoneUtils
{
    /**
     * 中国大陆手机号正则表达式
     */
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";
    
    /**
     * 手机号正则模式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile(PHONE_REGEX);

    /**
     * 验证手机号格式是否正确
     * 
     * @param phone 手机号
     * @return true 格式正确，false 格式错误
     */
    public static boolean isValidPhone(String phone)
    {
        if (StringUtils.isEmpty(phone))
        {
            return false;
        }
        return PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 生成6位数字验证码
     * 
     * @return 验证码
     */
    public static String generateSmsCode()
    {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++)
        {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 生成指定位数的数字验证码
     * 
     * @param length 验证码长度
     * @return 验证码
     */
    public static String generateSmsCode(int length)
    {
        if (length <= 0)
        {
            length = 6;
        }
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++)
        {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 隐藏手机号中间4位
     * 
     * @param phone 手机号
     * @return 隐藏后的手机号
     */
    public static String hiddenPhone(String phone)
    {
        if (StringUtils.isEmpty(phone) || phone.length() != 11)
        {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
