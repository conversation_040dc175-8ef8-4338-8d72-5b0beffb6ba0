package com.ruoyi.app.service.impl;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.ruoyi.app.config.AliyunSmsProperties;
import com.ruoyi.app.service.SmsService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.PhoneUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 短信服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class SmsServiceImpl implements SmsService
{
    private static final Logger log = LoggerFactory.getLogger(SmsServiceImpl.class);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AliyunSmsProperties aliyunSmsProperties;

    /**
     * 验证码有效期（分钟）
     */
    private static final int CODE_EXPIRE_TIME = 5;

    /**
     * 短信发送频率限制（秒）
     */
    private static final int SEND_RATE_LIMIT = 60;

    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 发送结果
     */
    @Override
    public boolean sendSmsCode(String phone, String code)
    {
        try
        {
            // 创建阿里云短信客户端
            Config config = new Config()
                .setAccessKeyId(aliyunSmsProperties.getAccessKeyId())
                .setAccessKeySecret(aliyunSmsProperties.getAccessKeySecret())
                .setRegionId(aliyunSmsProperties.getRegionId());

            Client client = new Client(config);

            // 构建发送请求
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName(aliyunSmsProperties.getSignName())
                .setTemplateCode(aliyunSmsProperties.getTemplateCode())
                .setTemplateParam("{\"code\":\"" + code + "\"}");

            // 发送短信
            SendSmsResponse response = client.sendSms(sendSmsRequest);
            
            if ("OK".equals(response.getBody().getCode()))
            {
                // 发送成功，将验证码存入Redis
                String cacheKey = CacheConstants.SMS_CODE_KEY + phone;
                redisCache.setCacheObject(cacheKey, code, CODE_EXPIRE_TIME, TimeUnit.MINUTES);
                
                // 设置发送频率限制
                String rateLimitKey = CacheConstants.SMS_RATE_LIMIT_KEY + phone;
                redisCache.setCacheObject(rateLimitKey, "1", SEND_RATE_LIMIT, TimeUnit.SECONDS);
                
                log.info("短信验证码发送成功，手机号：{}", phone);
                return true;
            }
            else
            {
                log.error("短信验证码发送失败，手机号：{}，错误码：{}，错误信息：{}", 
                    phone, response.getBody().getCode(), response.getBody().getMessage());
                return false;
            }
        }
        catch (Exception e)
        {
            log.error("短信验证码发送异常，手机号：{}", phone, e);
            return false;
        }
    }

    /**
     * 验证短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 验证结果
     */
    @Override
    public boolean verifySmsCode(String phone, String code)
    {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(code))
        {
            return false;
        }

        String cacheKey = CacheConstants.SMS_CODE_KEY + phone;
        String cachedCode = redisCache.getCacheObject(cacheKey);
        
        if (StringUtils.isEmpty(cachedCode))
        {
            log.warn("验证码已过期或不存在，手机号：{}", phone);
            return false;
        }

        boolean result = code.equals(cachedCode);
        if (result)
        {
            // 验证成功后删除验证码
            redisCache.deleteObject(cacheKey);
            log.info("短信验证码验证成功，手机号：{}", phone);
        }
        else
        {
            log.warn("短信验证码验证失败，手机号：{}，输入验证码：{}", phone, code);
        }
        
        return result;
    }

    /**
     * 检查短信发送频率限制
     * 
     * @param phone 手机号
     * @return true 可以发送，false 发送过于频繁
     */
    @Override
    public boolean checkSendRate(String phone)
    {
        String rateLimitKey = CacheConstants.SMS_RATE_LIMIT_KEY + phone;
        return !redisCache.hasKey(rateLimitKey);
    }

    /**
     * 生成并发送验证码
     * 
     * @param phone 手机号
     * @return 发送结果
     */
    @Override
    public boolean generateAndSendCode(String phone)
    {
        // 验证手机号格式
        if (!PhoneUtils.isValidPhone(phone))
        {
            log.warn("手机号格式不正确：{}", phone);
            return false;
        }

        // 检查发送频率
        if (!checkSendRate(phone))
        {
            log.warn("短信发送过于频繁，手机号：{}", phone);
            return false;
        }

        // 生成验证码
        String code = PhoneUtils.generateSmsCode();
        
        // 发送验证码
        return sendSmsCode(phone, code);
    }
}
