package com.ruoyi.app.controller;

import java.util.Map;
import java.util.HashMap;
import java.util.Enumeration;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.service.IMallPaymentService;
import com.ruoyi.fuguang.service.IOfflinePaymentService;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import java.math.BigDecimal;

/**
 * APP支付接口控制器
 * 提供支付宝支付回调、微信支付回调、线下支付等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP支付接口", description = "APP支付相关接口")
@RestController("appPaymentController")
@RequestMapping("/app/payment")
public class AppPaymentController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(AppPaymentController.class);

    @Autowired
    private IMallPaymentService mallPaymentService;

    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    @Autowired
    private IMerchantApplicationService merchantApplicationService;

    /**
     * 支付宝支付回调接口
     * 接收支付宝异步通知，处理支付结果
     *
     * @param request HTTP请求，包含支付宝回调参数
     * @return 处理结果，success表示成功，failure表示失败
     */
    @Anonymous
    @ApiOperation(value = "支付宝支付回调",
                  notes = "接收支付宝异步通知，验证签名并处理支付结果，更新订单状态")
    @ApiResponses({
        @ApiResponse(code = 200, message = "回调处理成功，返回success"),
        @ApiResponse(code = 500, message = "回调处理失败，返回failure")
    })
    @PostMapping("/alipay/notify")
    public String alipayNotify(@ApiParam(value = "HTTP请求", required = true) HttpServletRequest request)
    {
        try {
            log.info("收到支付宝支付回调");

            // 获取支付宝POST过来反馈信息
            Map<String, String> params = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();

            while (parameterNames.hasMoreElements()) {
                String parameterName = parameterNames.nextElement();
                String parameterValue = request.getParameter(parameterName);
                params.put(parameterName, parameterValue);
            }

            log.info("支付宝回调参数：{}", params);

            // 处理回调
            boolean result = mallPaymentService.handleAlipayCallback(params);

            if (result) {
                log.info("支付宝回调处理成功");
                return "success";
            } else {
                log.error("支付宝回调处理失败");
                return "failure";
            }
        } catch (Exception e) {
            log.error("支付宝回调处理异常", e);
            return "failure";
        }
    }

    /**
     * 支付宝支付结果查询
     */
    @ApiOperation("支付宝支付结果查询")
    @GetMapping("/alipay/query/{orderNo}")
    public AjaxResult queryAlipayResult(@PathVariable String orderNo)
    {
        try {
            String payStatus = mallPaymentService.getPaymentStatus(orderNo);
            
            Map<String, Object> data = new HashMap<>();
            data.put("orderNo", orderNo);
            data.put("payStatus", payStatus);
            
            String statusText;
            switch (payStatus) {
                case "0":
                    statusText = "待支付";
                    break;
                case "1":
                    statusText = "支付成功";
                    break;
                case "2":
                    statusText = "支付失败";
                    break;
                case "3":
                    statusText = "已退款";
                    break;
                default:
                    statusText = "未知状态";
                    break;
            }
            data.put("statusText", statusText);
            
            return success(data);
        } catch (Exception e) {
            log.error("查询支付结果异常", e);
            return error("查询支付结果失败：" + e.getMessage());
        }
    }

    /**
     * 模拟支付成功（仅用于测试）
     */
    @ApiOperation("模拟支付成功")
    @PostMapping("/test/success")
    public AjaxResult testPaymentSuccess(@RequestParam String orderNo)
    {
        try {
            // 仅在测试环境使用
            String tradeNo = "test_" + System.currentTimeMillis();
            int result = mallPaymentService.paymentSuccess(orderNo, tradeNo, "1");
            
            if (result > 0) {
                return success("模拟支付成功");
            } else {
                return error("模拟支付失败");
            }
        } catch (Exception e) {
            log.error("模拟支付成功异常", e);
            return error("模拟支付失败：" + e.getMessage());
        }
    }

    /**
     * 模拟支付失败（仅用于测试）
     */
    @ApiOperation("模拟支付失败")
    @PostMapping("/test/failure")
    public AjaxResult testPaymentFailure(@RequestParam String orderNo)
    {
        try {
            // 仅在测试环境使用
            int result = mallPaymentService.paymentFailed(orderNo);
            
            if (result > 0) {
                return success("模拟支付失败处理成功");
            } else {
                return error("模拟支付失败处理失败");
            }
        } catch (Exception e) {
            log.error("模拟支付失败异常", e);
            return error("模拟支付失败处理异常：" + e.getMessage());
        }
    }

    /**
     * 生成商家二维码
     */
    @ApiOperation("生成商家二维码")
    @GetMapping("/offline/merchant/{merchantId}/qrcode")
    public AjaxResult generateMerchantQrcode(@ApiParam("商家ID") @PathVariable Long merchantId)
    {
        try {
            Map<String, Object> result = offlinePaymentService.generateMerchantQrcode(merchantId);

            if ((Boolean) result.get("success")) {
                return success(result.get("qrcode"));
            } else {
                return error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("生成商家二维码异常，商家ID：{}", merchantId, e);
            return error("生成商家二维码失败：" + e.getMessage());
        }
    }

    /**
     * 获取商家二维码
     */
    @ApiOperation("获取商家二维码")
    @GetMapping("/offline/merchant/{merchantId}/qrcode/info")
    public AjaxResult getMerchantQrcode(@ApiParam("商家ID") @PathVariable Long merchantId)
    {
        try {
            return success(offlinePaymentService.getMerchantQrcode(merchantId));
        } catch (Exception e) {
            log.error("获取商家二维码异常，商家ID：{}", merchantId, e);
            return error("获取商家二维码失败：" + e.getMessage());
        }
    }

    /**
     * 创建线下支付订单
     */
    @ApiOperation("创建线下支付订单")
    @PostMapping("/offline/create")
    public AjaxResult createOfflinePayOrder(@RequestParam @ApiParam("商家ID") Long merchantId,
                                          @RequestParam @ApiParam("支付金额") BigDecimal payAmount)
    {
        try {
            if (merchantId == null || payAmount == null || payAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return error("参数错误");
            }

            Map<String, Object> result = offlinePaymentService.createOfflinePayOrder(merchantId, payAmount);

            if ((Boolean) result.get("success")) {
                return success(result);
            } else {
                return error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("创建线下支付订单异常，商家ID：{}，金额：{}", merchantId, payAmount, e);
            return error("创建线下支付订单失败：" + e.getMessage());
        }
    }

    /**
     * 线下支付支付宝回调
     */
    @Anonymous
    @ApiOperation("线下支付支付宝回调")
    @PostMapping("/offline/alipay/notify")
    public String offlineAlipayNotify(HttpServletRequest request)
    {
        try {
            log.info("收到线下支付支付宝回调");

            // 获取支付宝POST过来反馈信息
            Map<String, String> params = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();

            while (parameterNames.hasMoreElements()) {
                String parameterName = parameterNames.nextElement();
                String parameterValue = request.getParameter(parameterName);
                params.put(parameterName, parameterValue);
            }

            log.info("线下支付支付宝回调参数：{}", params);

            // 处理回调
            boolean result = offlinePaymentService.handleAlipayCallback(params);

            if (result) {
                log.info("线下支付支付宝回调处理成功");
                return "success";
            } else {
                log.error("线下支付支付宝回调处理失败");
                return "failure";
            }
        } catch (Exception e) {
            log.error("线下支付支付宝回调处理异常", e);
            return "failure";
        }
    }

    /**
     * 查询线下支付状态
     */
    @ApiOperation("查询线下支付状态")
    @GetMapping("/offline/query/{orderNo}")
    public AjaxResult queryOfflinePaymentStatus(@ApiParam("订单号") @PathVariable String orderNo)
    {
        try {
            String payStatus = offlinePaymentService.getPaymentStatus(orderNo);

            Map<String, Object> data = new HashMap<>();
            data.put("orderNo", orderNo);
            data.put("payStatus", payStatus);

            String statusText;
            switch (payStatus) {
                case "0":
                    statusText = "待支付";
                    break;
                case "1":
                    statusText = "支付成功";
                    break;
                case "2":
                    statusText = "支付失败";
                    break;
                case "3":
                    statusText = "已退款";
                    break;
                default:
                    statusText = "未知状态";
                    break;
            }
            data.put("statusText", statusText);

            return success(data);
        } catch (Exception e) {
            log.error("查询线下支付状态异常，订单号：{}", orderNo, e);
            return error("查询支付状态失败：" + e.getMessage());
        }
    }

    /**
     * 模拟线下支付成功（仅用于测试）
     */
    @ApiOperation("模拟线下支付成功")
    @PostMapping("/offline/test/success")
    public AjaxResult testOfflinePaymentSuccess(@RequestParam String orderNo)
    {
        try {
            // 仅在测试环境使用
            String tradeNo = "offline_test_" + System.currentTimeMillis();
            int result = offlinePaymentService.paymentSuccess(orderNo, tradeNo);

            if (result > 0) {
                return success("模拟线下支付成功");
            } else {
                return error("模拟线下支付失败");
            }
        } catch (Exception e) {
            log.error("模拟线下支付成功异常", e);
            return error("模拟线下支付失败：" + e.getMessage());
        }
    }
}
