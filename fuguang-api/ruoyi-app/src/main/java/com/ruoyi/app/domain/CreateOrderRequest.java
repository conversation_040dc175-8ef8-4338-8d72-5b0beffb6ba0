package com.ruoyi.app.domain;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 创建订单请求对象
 * 
 * <AUTHOR>
 */
@ApiModel("创建订单请求")
public class CreateOrderRequest
{
    /** 收货地址ID */
    @ApiModelProperty("收货地址ID")
    private Long addressId;

    /** 配送费 */
    @ApiModelProperty("配送费")
    private BigDecimal deliveryFee;

    /** 订单备注 */
    @ApiModelProperty("订单备注")
    private String remark;

    /** 订单商品列表 */
    @ApiModelProperty("订单商品列表")
    private List<CreateOrderItem> orderItems;

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<CreateOrderItem> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<CreateOrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    /**
     * 订单商品项
     */
    @ApiModel("订单商品项")
    public static class CreateOrderItem
    {
        /** 商品ID */
        @ApiModelProperty("商品ID")
        private Long productId;

        /** 商品规格ID */
        @ApiModelProperty("商品规格ID")
        private Long specId;

        /** 购买数量 */
        @ApiModelProperty("购买数量")
        private Integer quantity;

        public Long getProductId() {
            return productId;
        }

        public void setProductId(Long productId) {
            this.productId = productId;
        }

        public Long getSpecId() {
            return specId;
        }

        public void setSpecId(Long specId) {
            this.specId = specId;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }
}
