package com.ruoyi.app.service;

/**
 * 短信服务接口
 * 
 * <AUTHOR>
 */
public interface SmsService
{
    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 发送结果
     */
    boolean sendSmsCode(String phone, String code);

    /**
     * 验证短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 验证结果
     */
    boolean verifySmsCode(String phone, String code);

    /**
     * 检查短信发送频率限制
     * 
     * @param phone 手机号
     * @return true 可以发送，false 发送过于频繁
     */
    boolean checkSendRate(String phone);

    /**
     * 生成并发送验证码
     * 
     * @param phone 手机号
     * @return 发送结果
     */
    boolean generateAndSendCode(String phone);
}
