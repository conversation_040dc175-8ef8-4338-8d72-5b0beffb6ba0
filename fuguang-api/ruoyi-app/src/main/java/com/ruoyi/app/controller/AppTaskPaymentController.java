package com.ruoyi.app.controller;

import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.TaskPayment;
import com.ruoyi.fuguang.domain.WithdrawRecord;
import com.ruoyi.fuguang.service.ITaskPaymentService;
import com.ruoyi.fuguang.service.IWithdrawService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP任务支付接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP任务支付接口")
@RestController("appTaskPaymentController")
@RequestMapping("/app/task/payment")
public class AppTaskPaymentController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(AppTaskPaymentController.class);

    @Autowired
    private ITaskPaymentService taskPaymentService;

    @Autowired
    private IWithdrawService withdrawService;

    /**
     * 支付宝支付回调
     */
    @Anonymous
    @ApiOperation("支付宝支付回调")
    @PostMapping("/alipay/notify")
    public String alipayNotify(HttpServletRequest request)
    {
        try {
            log.info("收到任务支付宝支付回调");
            
            // 获取支付宝POST过来反馈信息
            Map<String, String> params = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();
            
            while (parameterNames.hasMoreElements()) {
                String parameterName = parameterNames.nextElement();
                String parameterValue = request.getParameter(parameterName);
                params.put(parameterName, parameterValue);
            }
            
            log.info("任务支付宝回调参数：{}", params);
            
            // 处理回调
            boolean result = taskPaymentService.handleAlipayCallback(params);
            
            if (result) {
                log.info("任务支付宝回调处理成功");
                return "success";
            } else {
                log.error("任务支付宝回调处理失败");
                return "failure";
            }
        } catch (Exception e) {
            log.error("处理任务支付宝回调异常", e);
            return "failure";
        }
    }

    /**
     * 支付宝支付结果查询
     */
    @ApiOperation("支付宝支付结果查询")
    @GetMapping("/alipay/query/{orderNo}")
    public AjaxResult queryAlipayResult(@PathVariable String orderNo)
    {
        try {
            String payStatus = taskPaymentService.getPaymentStatus(orderNo);
            
            Map<String, Object> data = new HashMap<>();
            data.put("orderNo", orderNo);
            data.put("payStatus", payStatus);
            
            String statusText;
            switch (payStatus) {
                case "0":
                    statusText = "待支付";
                    break;
                case "1":
                    statusText = "支付成功";
                    break;
                case "2":
                    statusText = "支付失败";
                    break;
                case "3":
                    statusText = "已退款";
                    break;
                default:
                    statusText = "未知状态";
                    break;
            }
            data.put("statusText", statusText);
            
            return success(data);
        } catch (Exception e) {
            log.error("查询任务支付结果异常", e);
            return error("查询支付结果失败：" + e.getMessage());
        }
    }

    /**
     * 查询我的支付记录
     */
    @ApiOperation("查询我的支付记录")
    @GetMapping("/my-payments")
    public AjaxResult getMyPayments()
    {
        try {
            Long userId = getUserId();
            List<TaskPayment> payments = taskPaymentService.selectTaskPaymentListByUserId(userId);
            return success(payments);
        } catch (Exception e) {
            log.error("查询支付记录异常", e);
            return error("查询支付记录失败：" + e.getMessage());
        }
    }

    /**
     * 申请提现
     */
    @ApiOperation("申请提现")
    @PostMapping("/withdraw/apply")
    public AjaxResult applyWithdraw(
            @ApiParam("提现金额") @RequestParam BigDecimal withdrawAmount,
            @ApiParam("提现方式") @RequestParam(defaultValue = "1") String withdrawType,
            @ApiParam("收款账户") @RequestParam String payeeAccount,
            @ApiParam("收款人姓名") @RequestParam String payeeName)
    {
        try {
            Long userId = getUserId();
            Map<String, Object> result = withdrawService.applyWithdraw(userId, withdrawAmount, withdrawType, payeeAccount, payeeName);
            
            if ((Boolean) result.get("success")) {
                return success(result.get("message").toString(), result);
            } else {
                return error(result.get("message").toString());
            }
        } catch (Exception e) {
            log.error("申请提现异常", e);
            return error("申请提现失败：" + e.getMessage());
        }
    }

    /**
     * 查询提现记录
     */
    @ApiOperation("查询我的提现记录")
    @GetMapping("/withdraw/my-records")
    public AjaxResult getMyWithdrawRecords()
    {
        try {
            Long userId = getUserId();
            List<WithdrawRecord> records = withdrawService.selectWithdrawRecordsByUserId(userId);
            return success(records);
        } catch (Exception e) {
            log.error("查询提现记录异常", e);
            return error("查询提现记录失败：" + e.getMessage());
        }
    }

    /**
     * 查询提现状态
     */
    @ApiOperation("查询提现状态")
    @GetMapping("/withdraw/status/{withdrawNo}")
    public AjaxResult getWithdrawStatus(@PathVariable String withdrawNo)
    {
        try {
            String status = withdrawService.getWithdrawStatus(withdrawNo);
            
            Map<String, Object> data = new HashMap<>();
            data.put("withdrawNo", withdrawNo);
            data.put("status", status);
            
            String statusText;
            switch (status) {
                case "0":
                    statusText = "申请中";
                    break;
                case "1":
                    statusText = "处理中";
                    break;
                case "2":
                    statusText = "提现成功";
                    break;
                case "3":
                    statusText = "提现失败";
                    break;
                default:
                    statusText = "未知状态";
                    break;
            }
            data.put("statusText", statusText);
            
            return success(data);
        } catch (Exception e) {
            log.error("查询提现状态异常", e);
            return error("查询提现状态失败：" + e.getMessage());
        }
    }

    /**
     * 计算提现手续费
     */
    @ApiOperation("计算提现手续费")
    @GetMapping("/withdraw/calculate-fee")
    public AjaxResult calculateWithdrawFee(
            @ApiParam("提现金额") @RequestParam BigDecimal withdrawAmount,
            @ApiParam("提现方式") @RequestParam(defaultValue = "1") String withdrawType)
    {
        try {
            BigDecimal fee = withdrawService.calculateWithdrawFee(withdrawAmount, withdrawType);
            BigDecimal actualAmount = withdrawAmount.subtract(fee);
            
            Map<String, Object> data = new HashMap<>();
            data.put("withdrawAmount", withdrawAmount);
            data.put("withdrawFee", fee);
            data.put("actualAmount", actualAmount);
            
            return success(data);
        } catch (Exception e) {
            log.error("计算提现手续费异常", e);
            return error("计算提现手续费失败：" + e.getMessage());
        }
    }
}
