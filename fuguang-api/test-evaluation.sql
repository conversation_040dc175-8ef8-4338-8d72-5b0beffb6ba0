-- 测试任务评价功能的SQL脚本

-- 1. 创建测试数据：一个已完成的任务
INSERT INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(9999, '测试评价任务', '这是一个用于测试评价功能的任务', 50.00, '0', 2, 11, '0', '2', 1000, '测试用户1', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', 1001, '测试用户2', '北京市海淀区测试地址', '116.3', '39.9', '2025-08-24 10:00:00', '2025-08-24 12:00:00', 5, 0, 'admin', '2025-08-24 10:00:00', '', '2025-08-24 14:00:00', '测试评价任务');

-- 2. 插入一条测试评价记录
INSERT INTO `task_evaluation` (`evaluation_id`, `task_id`, `task_title`, `publisher_id`, `publisher_name`, `receiver_id`, `receiver_name`, `rating`, `evaluation_content`, `evaluation_tags`, `is_anonymous`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(1, 9999, '测试评价任务', 1000, '测试用户1', 1001, '测试用户2', 5, '服务态度很好，任务完成得很及时，非常满意！', '["服务态度好", "及时完成", "推荐"]', '0', '0', 'app_user_1000', '2025-08-24 14:30:00', '', NULL, '测试评价记录');

-- 3. 为接单人添加收到评价的时间线事件
INSERT INTO `app_user_timeline` (`timeline_id`, `user_id`, `event_type`, `event_title`, `event_desc`, `event_data`, `event_time`, `icon`, `color`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(9999, 1001, 'task_evaluation', '收到任务评价', '任务「测试评价任务」收到了5星评价：服务态度很好，任务完成得很及时，非常满意！', '{"taskId":9999,"rating":5,"evaluationId":1,"isAnonymous":"0"}', '2025-08-24 14:30:00', 'star', 'success', '0', 'system', '2025-08-24 14:30:00', '', NULL, NULL);

-- 4. 查询验证数据
-- 查询任务评价记录
SELECT * FROM task_evaluation WHERE task_id = 9999;

-- 查询接单人的评价统计
SELECT 
    receiver_id,
    receiver_name,
    COUNT(*) as total_evaluations,
    AVG(rating) as average_rating,
    SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star_count,
    SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star_count,
    SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star_count,
    SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star_count,
    SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star_count
FROM task_evaluation 
WHERE receiver_id = 1001 AND status = '0'
GROUP BY receiver_id, receiver_name;

-- 查询接单人的时间线事件
SELECT * FROM app_user_timeline WHERE user_id = 1001 AND event_type = 'task_evaluation' ORDER BY event_time DESC;

-- 5. 测试API接口的SQL查询
-- 模拟检查任务是否可以评价
SELECT 
    t.task_id,
    t.task_title,
    t.task_status,
    t.publisher_id,
    t.receiver_id,
    CASE 
        WHEN t.task_status != '2' THEN '任务未完成'
        WHEN e.evaluation_id IS NOT NULL THEN '已评价过'
        ELSE '可以评价'
    END as evaluation_status
FROM app_task t
LEFT JOIN task_evaluation e ON t.task_id = e.task_id
WHERE t.task_id = 9999 AND t.publisher_id = 1000;

-- 6. 清理测试数据的SQL（可选执行）
/*
DELETE FROM app_user_timeline WHERE timeline_id = 9999;
DELETE FROM task_evaluation WHERE evaluation_id = 1;
DELETE FROM app_task WHERE task_id = 9999;
*/

-- 7. 验证评价功能完整性
-- 检查评价表结构
DESCRIBE task_evaluation;

-- 检查是否有索引
SHOW INDEX FROM task_evaluation;

-- 检查菜单权限是否正确插入
SELECT menu_id, menu_name, parent_id, path, perms FROM sys_menu WHERE menu_name LIKE '%评价%' ORDER BY menu_id;

-- 检查角色菜单权限
SELECT rm.role_id, m.menu_name, m.perms 
FROM sys_role_menu rm 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE m.menu_name LIKE '%评价%' 
ORDER BY rm.role_id, m.menu_id;
